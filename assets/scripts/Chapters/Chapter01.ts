import { GameConfig } from "../GameConfig";
// 隐藏房间数据结构

export const Chapters01 = [
    // 1
    {
        // 出现的层数
        levels: [1, 50],
        hiddenRooms: {
            entranceGID: [5, 10], // 入口坐标列表
            treasureChests: [
                {
                    type: 'coin', // 宝箱类型
                    amount: 100,
                },
                {
                    type: 'diamond',
                    id: '17',
                    amount: 100,
                },
            ],
            roomItems: [
                {
                    type: 'coin', // 宝箱类型
                    amount: 20,
                },
                {
                    type: 'diamond',
                    id: '11', // 矿石id
                    amount: 10,
                },
                {
                    type: 'key',
                    amount: 1,
                },
            ], // 房间内的道具列表
        },

        // 地图配置，不能修改
        name: '沙漠',
        mapFile: 'maps/desert/dwarf01', // 对应的地图文件
        bgFrame01: 'chapterBg1TopFrame', // 对应Bg中配置背景图片
        bgFrame02: 'chapterBg1BottomFrame',
        bgIconFrame: 'chapterBg2IconFrame',
        bgColor: '#25181f',
        trapRockFrame: 'trapDwarfRockFrame', // Map中配置的图片
        trapSpikeFrame: 'trapDesetSpikeFrame',
        itemFrame: 'itemDwarfBucketFrame', // 问号砖块，未知道具

        // 下面为关卡配置
        random: false, // 需不需要随机，不论需不需要随机，下面的配置都要写，只是有的不用写，具体看注释，没说明的即使随机启用也不会影响

        itemHeart: 1, // 问号砖块，道具加血的血量
        itemLamp: 150, // 问号砖块，道具加矿灯的时长

        expMineDensity: 0.15, // 经验值矿石密度
        glowingTileDensity: 0.01, // 发光砖块密度
        itemKeyChance: 0.5, // 发光砖块，挖土出现钥匙几率
        itemDiamondChance: 0.2, // 发光砖块，挖土出现卡片碎片几率
        itemHiddenMoney: 5, // 发光砖块，金币数量，注意不是概率，如果上面三个不出，这个保底出金币

        dirtHP: 1, // 土和发光砖块（其实也是土）的血量
        itemHP: 1, // 问号砖块血量
        mineBaseHP: 2, // 矿石血量，
        rockHP: 20, // 石头血量
        // bombDamage: 2, // 炸弹伤害

        damages: {
            // 陷阱造成的伤害
            [GameConfig.blockKinds.TRAP_SPIKE]: 1,
            [GameConfig.blockKinds.TRAP_ROCK]: 1,
        },
        biome: {
            // 如果随机，这里写的会被修改
            count: 0, // 可以设到100
            size: 1, // 1, 2, 3: 依次最大，真实大小是随机的，每种尺寸有个范围
        },
        exits: {
            ladderTileID: 42, // 这几个是自动生成出口的关键tile id，可以在地图上点出来后，打开地图的tmx文件查看ID
        },
        tileIDs: {
            boxID: 65, // 宝箱tile id
            glowingID: 76, // 发光tile id
            mineID: 77, // 矿石tile id
            mineExpID: 78, // 经验矿石tile id
        },
        mines: {
            // 矿石配置
            kinds: [GameConfig.blockKinds.MINE_A20], // 随机的话下面的不用写，否则必须写
            // 具体配置格式参考:
            [GameConfig.blockKinds.MINE_A20]: {
                minLevel: 1, // 基于levels的最小值
                maxLevel: 50,
                density: 0.1, //0-1, 1表示每个tile都会有
            },
        },
        traps: {
            kinds: [], // 随机的话下面的不用写
        },
        items: {
            kinds: [
                GameConfig.blockKinds.ITEM_BOMB_GRENADE,
                GameConfig.blockKinds.ITEM_BOMB_FLYING,
                GameConfig.blockKinds.ITEM_BOMB_ZONE,
            ], // 随机的话下面的不用写
            [GameConfig.blockKinds.ITEM_BOMB_GRENADE]: {
                minLevel: 2,
                maxLevel: 50,
                density: 0.01,
            },
            [GameConfig.blockKinds.ITEM_BOMB_FLYING]: {
                minLevel: 2,
                maxLevel: 50,
                density: 0.01,
            },
            [GameConfig.blockKinds.ITEM_BOMB_ZONE]: {
                minLevel: 2,
                maxLevel: 50,
                density: 0.01,
            },
        },

        // 目标类型：mine: 矿石，depth：深度，coin：金币
        // 每关可能有多个目标
        targets: [
            // {kind: "mine", id: GameConfig.blockKinds.MINE_A20, count: 5}, // 挖22个20号矿石
            { kind: 'depth', count: 10 },
        ],
        // 奖励类型: coin: 金币, diamond: 钻石, key: 钥匙，card: 卡片碎片，exp: 经验值'
        rewards: [
            { kind: 'coin', count: 20 },
            { kind: 'exp', count: 118 },
        ],
    },

    // 2
    {
        levels: [51, 100], // 出现的层数

        hiddenRooms: {
            entranceGID: [5, 52], // 入口坐标列表
            treasureChests: [
                {
                    type: 'coin', // 宝箱类型
                    amount: 100,
                },
                {
                    type: 'ore',
                    id: '11',
                    amount: 100,
                },
            ],
            roomItems: [
                {
                    type: 'coin', // 宝箱类型
                },
                {
                    type: 'ore',
                    id: '11', // 矿石id
                },
            ], // 房间内的道具列表
        },
        // 地图配置，不能修改
        name: '冰原',
        mapFile: 'maps/desert/iceland02', // 对应的地图文件
        bgFrame01: 'chapterBg2TopFrame', // 对应Bg中配置背景图片
        bgFrame02: 'chapterBg2BottomFrame',
        bgIconFrame: 'chapterBg2IconFrame',
        bgColor: '#8590e0',
        trapRockFrame: 'trapIceRockFrame', // Map中配置的图片
        trapSpikeFrame: 'trapIceSpikeFrame',
        itemFrame: 'itemIceBucketFrame', // 问号砖块，未知道具

        // 下面为关卡配置
        random: false, // 需不需要随机，不论需不需要随机，下面的配置都要写，只是有的不用写，具体看注释，没说明的即使随机启用也不会影响

        itemHeart: 1, // 问号砖块，道具加血的血量
        itemLamp: 150, // 问号砖块，道具加矿灯的时长

        expMineDensity: 0.15, // 经验值矿石密度
        glowingTileDensity: 0.0000001, // 发光砖块密度
        itemKeyChance: 0.5, // 发光砖块，挖土出现钥匙几率
        itemDiamondChance: 0.2, // 发光砖块，挖土出现卡片碎片几率
        itemHiddenMoney: 5, // 发光砖块，金币数量，注意不是概率，如果上面三个不出，这个保底出金币

        dirtHP: 3, // 土和发光砖块（其实也是土）的血量
        itemHP: 3, // 问号砖块血量
        mineBaseHP: 6, // 矿石血量，
        rockHP: 30, // 石头血量
        // bombDamage: 2, // 炸弹伤害

        damages: {
            // 陷阱造成的伤害
            [GameConfig.blockKinds.TRAP_SPIKE]: 1,
            [GameConfig.blockKinds.TRAP_ROCK]: 1,
        },
        biome: {
            // 如果随机，这里写的会被修改
            count: 20, // 可以设到100
            size: 1, // 1, 2, 3: 依次最大，真实大小是随机的，每种尺寸有个范围
        },
        exits: {
            ladderTileID: 42, // 这几个是自动生成出口的关键tile id，可以在地图上点出来后，打开地图的tmx文件查看ID
        },
        tileIDs: {
            boxID: 65, // 宝箱tile id
            glowingID: 76, // 发光tile id
            mineID: 77, // 矿石tile id
            mineExpID: 78, // 经验矿石tile id
        },
        mines: {
            // 矿石配置
            kinds: [GameConfig.blockKinds.MINE_A24], // 随机的话下面的不用写，否则必须写
            // 具体配置格式参考:
            [GameConfig.blockKinds.MINE_A24]: {
                minLevel: 2,
                maxLevel: 50,
                density: 0.075, //0-1, 1表示每个tile都会有
            },
        },
        traps: {
            kinds: [GameConfig.blockKinds.TRAP_ROCK], // 随机的话下面的不用写
            // [GameConfig.blockKinds.TRAP_SPIKE]: {
            //     minLevel: 5,
            //     maxLevel: 250,
            //     density: 0.04,
            // },
            [GameConfig.blockKinds.TRAP_ROCK]: {
                minLevel: 3,
                maxLevel: 50,
                density: 0.04,
            },
        },
        items: {
            kinds: [
                GameConfig.blockKinds.ITEM_BOMB_GRENADE,
                GameConfig.blockKinds.ITEM_BOMB_FLYING,
            ], // 随机的话下面的不用写
            [GameConfig.blockKinds.ITEM_BOMB_GRENADE]: {
                minLevel: 2,
                maxLevel: 50,
                density: 0.02,
            },
            [GameConfig.blockKinds.ITEM_BOMB_FLYING]: {
                minLevel: 2,
                maxLevel: 50,
                density: 0.02,
            },
        },

        // 目标类型：mine: 矿石，depth：深度，coin：金币
        // 每关可能有多个目标
        targets: [
            { kind: 'depth', count: 20 }, // 到达30层级
        ],
        // 奖励类型: coin: 金币, diamond: 钻石, key: 钥匙，card: 卡片碎片，exp: 经验值'
        rewards: [
            { kind: 'coin', count: 40 },
            { kind: 'exp', count: 154 }, // 奖励经验值
        ],
    },
    // 3
    {
        levels: [101, 200], // 出现的层数

        hiddenRooms: {
            entranceGID: [5, 102], // 入口坐标列表
            treasureChests: [
                {
                    type: 'coin', // 宝箱类型
                    amount: 100,
                },
                {
                    type: 'ore',
                    id: '11',
                    amount: 100,
                },
            ],
            roomItems: [
                {
                    type: 'coin', // 宝箱类型
                },
                {
                    type: 'ore',
                    id: '11', // 矿石id
                },
            ], // 房间内的道具列表
        },

        name: '火山',
        mapFile: 'maps/desert/volcano03', // 对应的地图文件
        bgFrame01: 'chapterBg3TopFrame', // 对应Bg中配置背景图片
        bgFrame02: 'chapterBg3BottomFrame',
        bgIconFrame: 'chapterBg3IconFrame',
        bgColor: '#d54325',
        trapRockFrame: 'trapVolcanoRockFrame', // Map中配置的图片
        trapSpikeFrame: 'trapVolcanoSpikeFrame',
        itemFrame: 'itemVolcanoBucketFrame', // 问号砖块，未知道具

        random: false, // 需不需要随机，不论需不需要随机，下面的配置都要写，只是有的不用写，具体看注释，没说明的即使随机启用也不会影响

        itemHeart: 2, // 问号砖块，道具加血的血量
        itemLamp: 180, // 问号砖块，道具加矿灯的时长

        expMineDensity: 0.15, // 经验值矿石密度
        glowingTileDensity: 0.02, // 发光砖块密度
        itemKeyChance: 0.5, // 发光砖块，挖土出现钥匙几率
        itemDiamondChance: 0.2, // 发光砖块，挖土出现卡片碎片几率
        itemHiddenMoney: 5, // 发光砖块，金币数量，注意不是概率，如果上面三个不出，这个保底出金币

        dirtHP: 5, // 土和发光砖块（其实也是土）的血量
        itemHP: 5, // 问号砖块血量
        mineBaseHP: 10, // 矿石血量，包括经验值矿石
        rockHP: 50, // 石头血量
        // bombDamage: 5, // 炸弹伤害

        damages: {
            // 陷阱造成的伤害
            [GameConfig.blockKinds.TRAP_SPIKE]: 2,
            [GameConfig.blockKinds.TRAP_ROCK]: 2,
        },
        biome: {
            // 如果随机，这里写的会被修改
            count: 100, // 可以设到100
            size: 1, // 1, 2, 3: 依次最大，真实大小是随机的，每种尺寸有个范围
        },
        exits: {
            ladderTileID: 42, // 这几个是自动生成出口的关键tile id，可以在地图上点出来后，打开地图的tmx文件查看ID
        },
        tileIDs: {
            boxID: 67, // 宝箱tile id
            glowingID: 76, // 发光tile id
            mineID: 77, // 矿石tile id
            mineExpID: 78, // 经验矿石tile id
        },
        mines: {
            // 矿石配置
            kinds: [GameConfig.blockKinds.MINE_A24], // 随机的话下面的不用写，否则必须写
            // 具体配置格式参考:
            [GameConfig.blockKinds.MINE_A24]: {
                minLevel: 3,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.15, //0-1, 1表示每个tile都会有
            },
        },
        traps: {
            kinds: [GameConfig.blockKinds.TRAP_SPIKE, GameConfig.blockKinds.TRAP_ROCK], // 随机的话下面的不用写
            [GameConfig.blockKinds.TRAP_SPIKE]: {
                minLevel: 5,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.04,
            },
            [GameConfig.blockKinds.TRAP_ROCK]: {
                minLevel: 3,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.04,
            },
        },
        items: {
            kinds: [
                GameConfig.blockKinds.ITEM_HEART,
                GameConfig.blockKinds.ITEM_LAMP,
                GameConfig.blockKinds.ITEM_BOMB_FLYING,
                GameConfig.blockKinds.ITEM_BOMB_GRENADE,
            ], // 随机的话下面的不用写
            [GameConfig.blockKinds.ITEM_HEART]: {
                minLevel: 2,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.04,
            },
            [GameConfig.blockKinds.ITEM_LAMP]: {
                minLevel: 2,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.04,
            },
            [GameConfig.blockKinds.ITEM_BOMB_FLYING]: {
                minLevel: 2,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.02,
            },
            [GameConfig.blockKinds.ITEM_BOMB_GRENADE]: {
                minLevel: 2,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.02,
            },
        },
        // 目标类型：mine: 矿石，depth：深度，coin：金币
        // 每关可能有多个目标
        targets: [
            { kind: 'depth', count: 20 }, // 到达30层级
        ],
        // 奖励类型: coin: 金币, diamond: 钻石, key: 钥匙，card: 卡片碎片，exp: 经验值
        rewards: [
            { kind: 'coin', count: 200 },
            { kind: 'exp', count: 478 }, // 奖励经验值
        ],
    },

    // // 4
    {
        levels: [201, 300], // 出现的层数

        hiddenRooms: {
            entranceGID: [5, 202], // 入口坐标列表
            treasureChests: [
                {
                    type: 'coin', // 宝箱类型
                    amount: 100,
                },
                {
                    type: 'ore',
                    id: '11',
                    amount: 100,
                },
            ],
            roomItems: [
                {
                    type: 'coin', // 宝箱类型
                },
                {
                    type: 'ore',
                    id: '11', // 矿石id
                },
            ], // 房间内的道具列表
        },
        // 地图配置，不能修改
        name: '沙漠',
        mapFile: 'maps/desert/dwarf01', // 对应的地图文件
        bgFrame01: 'chapterBg1TopFrame', // 对应Bg中配置背景图片
        bgFrame02: 'chapterBg1BottomFrame',
        bgColor: '#25181f',
        trapRockFrame: 'trapDwarfRockFrame', // Map中配置的图片
        trapSpikeFrame: 'trapDesetSpikeFrame',
        itemFrame: 'itemDwarfBucketFrame', // 问号砖块，未知道具

        // 下面为关卡配置
        random: false, // 需不需要随机，不论需不需要随机，下面的配置都要写，只是有的不用写，具体看注释，没说明的即使随机启用也不会影响

        itemHeart: 1, // 问号砖块，道具加血的血量
        itemLamp: 150, // 问号砖块，道具加矿灯的时长

        expMineDensity: 0.15, // 经验值矿石密度
        glowingTileDensity: 0.0000001, // 发光砖块密度
        itemKeyChance: 0.5, // 发光砖块，挖土出现钥匙几率
        itemDiamondChance: 0.2, // 发光砖块，挖土出现卡片碎片几率
        itemHiddenMoney: 5, // 发光砖块，金币数量，注意不是概率，如果上面三个不出，这个保底出金币

        dirtHP: 3, // 土和发光砖块（其实也是土）的血量
        itemHP: 3, // 问号砖块血量
        mineBaseHP: 6, // 矿石血量，
        rockHP: 30, // 石头血量
        // bombDamage: 3, // 炸弹伤害

        damages: {
            // 陷阱造成的伤害
            [GameConfig.blockKinds.TRAP_SPIKE]: 1,
            [GameConfig.blockKinds.TRAP_ROCK]: 1,
        },
        biome: {
            // 如果随机，这里写的会被修改
            count: 30, // 可以设到100
            size: 1, // 1, 2, 3: 依次最大，真实大小是随机的，每种尺寸有个范围
        },
        exits: {
            ladderTileID: 42, // 这几个是自动生成出口的关键tile id，可以在地图上点出来后，打开地图的tmx文件查看ID
        },
        tileIDs: {
            boxID: 65, // 宝箱tile id
            glowingID: 76, // 发光tile id
            mineID: 77, // 矿石tile id
            mineExpID: 78, // 经验矿石tile id
        },
        mines: {
            // 矿石配置
            kinds: [GameConfig.blockKinds.MINE_A20], // 随机的话下面的不用写，否则必须写
            // 具体配置格式参考:
            [GameConfig.blockKinds.MINE_A20]: {
                minLevel: 1,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.085, //0-1, 1表示每个tile都会有
            },
        },
        traps: {
            kinds: [GameConfig.blockKinds.TRAP_SPIKE], // 随机的话下面的不用写
            [GameConfig.blockKinds.TRAP_SPIKE]: {
                minLevel: 3,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.06,
            },
            // [GameConfig.blockKinds.TRAP_ROCK]: {
            //     minLevel: 3,
            //     maxLevel: 250,
            //     density: 0.06,
            // },
        },
        items: {
            kinds: [
                GameConfig.blockKinds.ITEM_BOMB_GRENADE,
                GameConfig.blockKinds.ITEM_BOMB_FLYING,
            ], // 随机的话下面的不用写
            [GameConfig.blockKinds.ITEM_BOMB_GRENADE]: {
                minLevel: 2,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.02,
            },
            [GameConfig.blockKinds.ITEM_BOMB_FLYING]: {
                minLevel: 2,
                maxLevel: GameConfig.mapMaxLevel - 5,
                density: 0.02,
            },
        },

        // 目标类型：mine: 矿石，depth：深度，coin：金币
        // 每关可能有多个目标
        targets: [
            { kind: 'depth', count: 40 }, // 挖22个20号矿石
        ],
        // 奖励类型: coin: 金币, diamond: 钻石, key: 钥匙，card: 卡片碎片，exp: 经验值'
        rewards: [
            { kind: 'key', count: 2 }, // 钥匙和宝箱配对，分三个等级的钥匙
            { kind: 'exp', count: 226 }, // 奖励经验值
        ],
    },

    // // 5
    // {
    //     // 地图配置，不能修改
    //     name: '沙漠',
    //     mapFile: 'maps/desert/dwarf01', // 对应的地图文件
    //     bgFrame01: 'chapterBg1TopFrame', // 对应Bg中配置背景图片
    //     bgFrame02: 'chapterBg1BottomFrame',
    //     bgColor: '#25181f',
    //     trapRockFrame: 'trapDwarfRockFrame', // Map中配置的图片
    //     trapSpikeFrame: 'trapDesetSpikeFrame',
    //     itemFrame: 'itemDwarfBucketFrame', // 问号砖块，未知道具

    //     // 下面为关卡配置
    //     random: false, // 需不需要随机，不论需不需要随机，下面的配置都要写，只是有的不用写，具体看注释，没说明的即使随机启用也不会影响

    //     itemHeart: 1, // 问号砖块，道具加血的血量
    //     itemLamp: 150, // 问号砖块，道具加矿灯的时长

    //     expMineDensity: 0.15, // 经验值矿石密度
    //     glowingTileDensity: 0.0000001, // 发光砖块密度
    //     itemKeyChance: 0.5, // 发光砖块，挖土出现钥匙几率
    //     itemDiamondChance: 0.2, // 发光砖块，挖土出现卡片碎片几率
    //     itemHiddenMoney: 5, // 发光砖块，金币数量，注意不是概率，如果上面三个不出，这个保底出金币

    //     dirtHP: 3, // 土和发光砖块（其实也是土）的血量
    //     itemHP: 3, // 问号砖块血量
    //     mineBaseHP: 6, // 矿石血量，
    //     rockHP: 30, // 石头血量
    //     // bombDamage: 3, // 炸弹伤害

    //     damages: {
    //         // 陷阱造成的伤害
    //         [GameConfig.blockKinds.TRAP_SPIKE]: 1,
    //         [GameConfig.blockKinds.TRAP_ROCK]: 1,
    //     },
    //     biome: {
    //         // 如果随机，这里写的会被修改
    //         count: 30, // 可以设到100
    //         size: 1, // 1, 2, 3: 依次最大，真实大小是随机的，每种尺寸有个范围
    //     },
    //     exits: {
    //         ladderTileID: 42, // 这几个是自动生成出口的关键tile id，可以在地图上点出来后，打开地图的tmx文件查看ID
    //     },
    //     tileIDs: {
    //         boxID: 65, // 宝箱tile id
    //         glowingID: 76, // 发光tile id
    //         mineID: 77, // 矿石tile id
    //         mineExpID: 78, // 经验矿石tile id
    //     },
    //     mines: {
    //         // 矿石配置
    //         kinds: [GameConfig.blockKinds.MINE_A20], // 随机的话下面的不用写，否则必须写
    //         // 具体配置格式参考:
    //         [GameConfig.blockKinds.MINE_A20]: {
    //             minLevel: 1,
    //             maxLevel: GameConfig.mapMaxLevel - 5,
    //             density: 0.085, //0-1, 1表示每个tile都会有
    //         },
    //     },
    //     traps: {
    //         kinds: [GameConfig.blockKinds.TRAP_SPIKE, GameConfig.blockKinds.TRAP_ROCK], // 随机的话下面的不用写
    //         [GameConfig.blockKinds.TRAP_SPIKE]: {
    //             minLevel: 5,
    //             maxLevel: GameConfig.mapMaxLevel - 5,
    //             density: 0.04,
    //         },
    //         [GameConfig.blockKinds.TRAP_ROCK]: {
    //             minLevel: 3,
    //             maxLevel: GameConfig.mapMaxLevel - 5,
    //             density: 0.04,
    //         },
    //     },
    //     items: {
    //         kinds: [
    //             GameConfig.blockKinds.ITEM_BOMB_GRENADE,
    //             GameConfig.blockKinds.ITEM_BOMB_FLYING,
    //             // GameConfig.blockKinds.ITEM_BOMB_FLYING,
    //             // GameConfig.blockKinds.ITEM_BOMB_GRENADE,
    //             // GameConfig.blockKinds.ITEM_BOMB_ZONE,
    //         ], // 随机的话下面的不用写
    //         [GameConfig.blockKinds.ITEM_BOMB_GRENADE]: {
    //             minLevel: 2,
    //             maxLevel: GameConfig.mapMaxLevel - 5,
    //             density: 0.02,
    //         },
    //         [GameConfig.blockKinds.ITEM_BOMB_FLYING]: {
    //             minLevel: 2,
    //             maxLevel: GameConfig.mapMaxLevel - 5,
    //             density: 0.02,
    //         },
    //         // [GameConfig.blockKinds.ITEM_BOMB_FLYING]: {
    //         //     minLevel: 2,
    //         //     maxLevel: 250,
    //         //     density: 0.04,
    //         // },
    //         // [GameConfig.blockKinds.ITEM_BOMB_GRENADE]: {
    //         //     minLevel: 2,
    //         //     maxLevel: 250,
    //         //     density: 0.04,
    //         // },
    //         // [GameConfig.blockKinds.ITEM_BOMB_ZONE]: {
    //         //     minLevel: 2,
    //         //     maxLevel: 250,
    //         //     density: 0.04,
    //         // },
    //     },

    //     // 目标类型：mine: 矿石，depth：深度，coin：金币
    //     // 每关可能有多个目标
    //     targets: [
    //         { kind: 'depth', count: 50 }, // 到达30层级
    //         // { kind: "coin", count: 200 }, // 赚取200金币
    //     ],
    //     // 奖励类型: coin: 金币, diamond: 钻石, key: 钥匙，card: 卡片碎片，exp: 经验值'
    //     rewards: [
    //         { kind: 'card', id: 1_01, count: 1 }, // 卡片，id为卡片编号
    //         { kind: 'exp', count: 262 }, // 奖励经验值
    //     ],
    // },

    // // 6
    // {
    //     // 地图配置，不能修改
    //     name: "沙漠",
    //     mapFile: "maps/desert/dwarf01", // 对应的地图文件
    //     bgFrame01: "chapterBg1TopFrame", // 对应Bg中配置背景图片
    //     bgFrame02: "chapterBg1BottomFrame",
    //     bgColor: "#25181f",
    //     trapRockFrame: "trapDwarfRockFrame", // Map中配置的图片
    //     trapSpikeFrame: "trapDesetSpikeFrame",
    //     itemFrame: "itemDwarfBucketFrame", // 问号砖块，未知道具

    //     // 下面为关卡配置
    //     random: false, // 需不需要随机，不论需不需要随机，下面的配置都要写，只是有的不用写，具体看注释，没说明的即使随机启用也不会影响

    //     itemHeart: 1, // 问号砖块，道具加血的血量
    //     itemLamp: 150, // 问号砖块，道具加矿灯的时长

    //     expMineDensity: 0.2, // 经验值矿石密度
    //     glowingTileDensity: 0.00001, // 发光砖块密度
    //     itemKeyChance: 0.5, // 发光砖块，挖土出现钥匙几率
    //     itemDiamondChance: 0.2, // 发光砖块，挖土出现卡片碎片几率
    //     itemHiddenMoney: 5, // 发光砖块，金币数量，注意不是概率，如果上面三个不出，这个保底出金币

    //     dirtHP: 4, // 土和发光砖块（其实也是土）的血量
    //     itemHP: 4, // 问号砖块血量
    //     mineBaseHP: 6, // 矿石血量，包括经验值矿石
    //     rockHP: 40, // 石头血量

    //     damages: { // 陷阱造成的伤害
    //         [GameConfig.blockKinds.TRAP_SPIKE]: 1,
    //         [GameConfig.blockKinds.TRAP_ROCK]: 1,
    //     },
    //     biome: { // 如果随机，这里写的会被修改
    //         count: 60, // 可以设到100
    //         size: 1, // 1, 2, 3: 依次最大，真实大小是随机的，每种尺寸有个范围
    //     },
    //     exits: {
    //         ladderTileID: 42, // 这几个是自动生成出口的关键tile id，可以在地图上点出来后，打开地图的tmx文件查看ID
    //     },
    //     mines: { // 矿石配置
    //         kinds: [GameConfig.blockKinds.MINE_A20], // 随机的话下面的不用写，否则必须写
    //         // 具体配置格式参考:
    //         [GameConfig.blockKinds.MINE_A20]: {
    //             minLevel: 1,
    //             maxLevel: 250,
    //             density: 0.1, //0-1, 1表示每个tile都会有
    //         },
    //     },
    //     traps: {
    //         kinds: [GameConfig.blockKinds.TRAP_SPIKE, GameConfig.blockKinds.TRAP_ROCK],// 随机的话下面的不用写
    //         [GameConfig.blockKinds.TRAP_SPIKE]: {
    //             minLevel: 5,
    //             maxLevel: 250,
    //             density: 0.04,
    //         },
    //         [GameConfig.blockKinds.TRAP_ROCK]: {
    //             minLevel: 3,
    //             maxLevel: 250,
    //             density: 0.04,
    //         },
    //     },
    //     items: {
    //         kinds: [
    //             GameConfig.blockKinds.ITEM_HEART,
    //             GameConfig.blockKinds.ITEM_LAMP,
    //         ], // 随机的话下面的不用写
    //         [GameConfig.blockKinds.ITEM_HEART]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //         [GameConfig.blockKinds.ITEM_LAMP]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //     },

    //     // 目标类型：mine: 矿石，depth：深度，coin：金币
    //     // 每关可能有多个目标
    //     targets: [
    //         // { kind: "mine", id: GameConfig.blockKinds.MINE_A19, count: 20 }, // 挖20个19号矿石
    //         { kind: "mine", id: GameConfig.blockKinds.MINE_A20, count: 15 }, // 挖22个20号矿石
    //         { kind: "depth", count: 20 }, // 到达30层级
    //         // { kind: "coin", count: 200 }, // 赚取200金币
    //     ],
    //     // 奖励类型: coin: 金币, diamond: 钻石, key: 钥匙，card: 卡片碎片，exp: 经验值'
    //     rewards: [
    //         { kind: "key", count: 2 }, // 钥匙和宝箱配对，分三个等级的钥匙
    //         { kind: "exp", count: 700 }, // 奖励经验值
    //     ]
    // },
    // // 7
    // {
    //     // 地图配置，不能修改
    //     name: "沙漠",
    //     mapFile: "maps/desert/dwarf01", // 对应的地图文件
    //     bgFrame01: "chapterBg1TopFrame", // 对应Bg中配置背景图片
    //     bgFrame02: "chapterBg1BottomFrame",
    //     bgColor: "#25181f",
    //     trapRockFrame: "trapDwarfRockFrame", // Map中配置的图片
    //     trapSpikeFrame: "trapDesetSpikeFrame",
    //     itemFrame: "itemDwarfBucketFrame", // 问号砖块，未知道具

    //     // 下面为关卡配置
    //     random: false, // 需不需要随机，不论需不需要随机，下面的配置都要写，只是有的不用写，具体看注释，没说明的即使随机启用也不会影响

    //     itemHeart: 1, // 问号砖块，道具加血的血量
    //     itemLamp: 150, // 问号砖块，道具加矿灯的时长

    //     expMineDensity: 0.2, // 经验值矿石密度
    //     glowingTileDensity: 0.00001, // 发光砖块密度
    //     itemKeyChance: 0.5, // 发光砖块，挖土出现钥匙几率
    //     itemDiamondChance: 0.2, // 发光砖块，挖土出现卡片碎片几率
    //     itemHiddenMoney: 5, // 发光砖块，金币数量，注意不是概率，如果上面三个不出，这个保底出金币

    //     dirtHP: 4, // 土和发光砖块（其实也是土）的血量
    //     itemHP: 4, // 问号砖块血量
    //     mineBaseHP: 6, // 矿石血量，包括经验值矿石
    //     rockHP: 40, // 石头血量

    //     damages: { // 陷阱造成的伤害
    //         [GameConfig.blockKinds.TRAP_SPIKE]: 1,
    //         [GameConfig.blockKinds.TRAP_ROCK]: 1,
    //     },
    //     biome: { // 如果随机，这里写的会被修改
    //         count: 70, // 可以设到100
    //         size: 1, // 1, 2, 3: 依次最大，真实大小是随机的，每种尺寸有个范围
    //     },
    //     exits: {
    //         ladderTileID: 42, // 这几个是自动生成出口的关键tile id，可以在地图上点出来后，打开地图的tmx文件查看ID
    //     },
    //     mines: { // 矿石配置
    //         kinds: [GameConfig.blockKinds.MINE_A20, GameConfig.blockKinds.MINE_A26], // 随机的话下面的不用写，否则必须写
    //         // 具体配置格式参考:
    //         [GameConfig.blockKinds.MINE_A20]: {
    //             minLevel: 1,
    //             maxLevel: 250,
    //             density: 0.08, //0-1, 1表示每个tile都会有
    //         },
    //         [GameConfig.blockKinds.MINE_A26]: {
    //             minLevel: 10,
    //             maxLevel: 250,
    //             density: 0.04, //0-1, 1表示每个tile都会有
    //         },
    //     },
    //     traps: {
    //         kinds: [GameConfig.blockKinds.TRAP_SPIKE, GameConfig.blockKinds.TRAP_ROCK],// 随机的话下面的不用写
    //         [GameConfig.blockKinds.TRAP_SPIKE]: {
    //             minLevel: 5,
    //             maxLevel: 250,
    //             density: 0.04,
    //         },
    //         [GameConfig.blockKinds.TRAP_ROCK]: {
    //             minLevel: 3,
    //             maxLevel: 250,
    //             density: 0.04,
    //         },
    //     },
    //     items: {
    //         kinds: [
    //             GameConfig.blockKinds.ITEM_HEART,
    //             GameConfig.blockKinds.ITEM_LAMP,
    //         ], // 随机的话下面的不用写
    //         [GameConfig.blockKinds.ITEM_HEART]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //         [GameConfig.blockKinds.ITEM_LAMP]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //     },

    //     // 目标类型：mine: 矿石，depth：深度，coin：金币
    //     // 每关可能有多个目标
    //     targets: [
    //         { kind: "mine", id: GameConfig.blockKinds.MINE_A20, count: 20 }, // 挖20个19号矿石
    //         { kind: "mine", id: GameConfig.blockKinds.MINE_A26, count: 5 }, // 挖22个20号矿石
    //         // { kind: "depth", count: 20 }, // 到达30层级
    //         // { kind: "coin", count: 200 }, // 赚取200金币
    //     ],
    //     // 奖励类型: coin: 金币, diamond: 钻石, key: 钥匙，card: 卡片碎片，exp: 经验值'
    //     rewards: [
    //         { kind: "coin", count: 30 },
    //         { kind: "diamond", count: 2 },
    //         // { kind: "key", count: 1 }, // 钥匙和宝箱配对，分三个等级的钥匙
    //         // {kind: "cardPart", id: 1_01, count: 1}, // 卡片碎片，id为卡片编号
    //         // {kind: "card", id: 1_01, count: 1}, // 卡片，id为卡片编号
    //         { kind: "exp", count: 700 }, // 奖励经验值
    //     ]
    // },
    // // 8
    // {
    //     // 地图配置，不能修改
    //     name: "沙漠",
    //     mapFile: "maps/desert/dwarf01", // 对应的地图文件
    //     bgFrame01: "chapterBg1TopFrame", // 对应Bg中配置背景图片
    //     bgFrame02: "chapterBg1BottomFrame",
    //     bgColor: "#25181f",
    //     trapRockFrame: "trapDwarfRockFrame", // Map中配置的图片
    //     trapSpikeFrame: "trapDesetSpikeFrame",
    //     itemFrame: "itemDwarfBucketFrame", // 问号砖块，未知道具

    //     // 下面为关卡配置
    //     random: false, // 需不需要随机，不论需不需要随机，下面的配置都要写，只是有的不用写，具体看注释，没说明的即使随机启用也不会影响

    //     itemHeart: 1, // 问号砖块，道具加血的血量
    //     itemLamp: 150, // 问号砖块，道具加矿灯的时长

    //     expMineDensity: 0.2, // 经验值矿石密度
    //     glowingTileDensity: 0.00001, // 发光砖块密度
    //     itemKeyChance: 0.5, // 发光砖块，挖土出现钥匙几率
    //     itemDiamondChance: 0.2, // 发光砖块，挖土出现卡片碎片几率
    //     itemHiddenMoney: 5, // 发光砖块，金币数量，注意不是概率，如果上面三个不出，这个保底出金币

    //     dirtHP: 4, // 土和发光砖块（其实也是土）的血量
    //     itemHP: 4, // 问号砖块血量
    //     mineBaseHP: 6, // 矿石血量，包括经验值矿石
    //     rockHP: 40, // 石头血量

    //     damages: { // 陷阱造成的伤害
    //         [GameConfig.blockKinds.TRAP_SPIKE]: 2,
    //         [GameConfig.blockKinds.TRAP_ROCK]: 2,
    //     },
    //     biome: { // 如果随机，这里写的会被修改
    //         count: 90, // 可以设到100
    //         size: 1, // 1, 2, 3: 依次最大，真实大小是随机的，每种尺寸有个范围
    //     },
    //     exits: {
    //         ladderTileID: 42, // 这几个是自动生成出口的关键tile id，可以在地图上点出来后，打开地图的tmx文件查看ID
    //     },

    //     mines: { // 矿石配置
    //         kinds: [GameConfig.blockKinds.MINE_A20], // 随机的话下面的不用写，否则必须写
    //         // 具体配置格式参考:
    //         [GameConfig.blockKinds.MINE_A20]: {
    //             minLevel: 1,
    //             maxLevel: 250,
    //             density: 0.08, //0-1, 1表示每个tile都会有
    //         },
    //     },
    //     traps: {
    //         kinds: [GameConfig.blockKinds.TRAP_SPIKE, GameConfig.blockKinds.TRAP_ROCK],// 随机的话下面的不用写
    //         [GameConfig.blockKinds.TRAP_SPIKE]: {
    //             minLevel: 5,
    //             maxLevel: 250,
    //             density: 0.04,
    //         },
    //         [GameConfig.blockKinds.TRAP_ROCK]: {
    //             minLevel: 3,
    //             maxLevel: 250,
    //             density: 0.04,
    //         },
    //     },
    //     items: {
    //         kinds: [
    //             GameConfig.blockKinds.ITEM_HEART,
    //             GameConfig.blockKinds.ITEM_LAMP,
    //             // GameConfig.blockKinds.ITEM_BOMB_FLYING,
    //             // GameConfig.blockKinds.ITEM_BOMB_GRENADE,
    //             // GameConfig.blockKinds.ITEM_BOMB_ZONE
    //         ], // 随机的话下面的不用写
    //         [GameConfig.blockKinds.ITEM_HEART]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //         [GameConfig.blockKinds.ITEM_LAMP]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },

    //     },

    //     // 目标类型：mine: 矿石，depth：深度，coin：金币
    //     // 每关可能有多个目标
    //     targets: [
    //         { kind: "mine", id: GameConfig.blockKinds.MINE_A19, count: 20 }, // 挖20个19号矿石
    //         // { kind: "mine", id: GameConfig.blockKinds.MINE_A20, count: 22 }, // 挖22个20号矿石
    //         // { kind: "depth", count: 20 }, // 到达30层级
    //         // { kind: "coin", count: 30 }, // 赚取200金币
    //     ],
    //     // 奖励类型: coin: 金币, diamond: 钻石, key: 钥匙，card: 卡片碎片，exp: 经验值'
    //     rewards: [
    //         { kind: "coin", count: 30 },
    //         // { kind: "diamond", count: 3 },
    //         // { kind: "key", count: 1 }, // 钥匙和宝箱配对，分三个等级的钥匙
    //         // { kind: "cardPart", id: 1_01, count: 2}, // 卡片碎片，id为卡片编号
    //         // {kind: "card", id: 1_01, count: 1}, // 卡片，id为卡片编号
    //         { kind: "exp", count: 700 }, // 奖励经验值
    //     ]
    // },
    // // 9
    // {
    //     // 地图配置，不能修改
    //     name: "沙漠",
    //     mapFile: "maps/desert/dwarf01", // 对应的地图文件
    //     bgFrame01: "chapterBg1TopFrame", // 对应Bg中配置背景图片
    //     bgFrame02: "chapterBg1BottomFrame",
    //     bgColor: "#25181f",
    //     trapRockFrame: "trapDwarfRockFrame", // Map中配置的图片
    //     trapSpikeFrame: "trapDesetSpikeFrame",
    //     itemFrame: "itemDwarfBucketFrame", // 问号砖块，未知道具

    //     // 下面为关卡配置
    //     random: false, // 需不需要随机，不论需不需要随机，下面的配置都要写，只是有的不用写，具体看注释，没说明的即使随机启用也不会影响

    //     itemHeart: 1, // 问号砖块，道具加血的血量
    //     itemLamp: 150, // 问号砖块，道具加矿灯的时长

    //     expMineDensity: 0.2, // 经验值矿石密度
    //     glowingTileDensity: 0.00001, // 发光砖块密度
    //     itemKeyChance: 0.5, // 发光砖块，挖土出现钥匙几率
    //     itemDiamondChance: 0.2, // 发光砖块，挖土出现卡片碎片几率
    //     itemHiddenMoney: 5, // 发光砖块，金币数量，注意不是概率，如果上面三个不出，这个保底出金币

    //     dirtHP: 4, // 土和发光砖块（其实也是土）的血量
    //     itemHP: 4, // 问号砖块血量
    //     mineBaseHP: 6, // 矿石血量，包括经验值矿石
    //     rockHP: 40, // 石头血量

    //     damages: { // 陷阱造成的伤害
    //         [GameConfig.blockKinds.TRAP_SPIKE]: 2,
    //         [GameConfig.blockKinds.TRAP_ROCK]: 2,
    //     },
    //     biome: { // 如果随机，这里写的会被修改
    //         count: 100, // 可以设到100
    //         size: 1, // 1, 2, 3: 依次最大，真实大小是随机的，每种尺寸有个范围
    //     },
    //     exits: {
    //         ladderTileID: 42, // 这几个是自动生成出口的关键tile id，可以在地图上点出来后，打开地图的tmx文件查看ID
    //     },
    //     mines: { // 矿石配置
    //         kinds: [GameConfig.blockKinds.MINE_A20, GameConfig.blockKinds.MINE_A26], // 随机的话下面的不用写，否则必须写
    //         // 具体配置格式参考:
    //         [GameConfig.blockKinds.MINE_A20]: {
    //             minLevel: 1,
    //             maxLevel: 250,
    //             density: 0.15, //0-1, 1表示每个tile都会有
    //         },
    //         [GameConfig.blockKinds.MINE_A26]: {
    //             minLevel: 5,
    //             maxLevel: 250,
    //             density: 0.1, //0-1, 1表示每个tile都会有
    //         },
    //     },
    //     traps: {
    //         kinds: [GameConfig.blockKinds.TRAP_SPIKE, GameConfig.blockKinds.TRAP_ROCK],// 随机的话下面的不用写
    //         [GameConfig.blockKinds.TRAP_SPIKE]: {
    //             minLevel: 5,
    //             maxLevel: 250,
    //             density: 0.04,
    //         },
    //         [GameConfig.blockKinds.TRAP_ROCK]: {
    //             minLevel: 3,
    //             maxLevel: 250,
    //             density: 0.04,
    //         },
    //     },
    //     items: {
    //         kinds: [
    //             GameConfig.blockKinds.ITEM_HEART,
    //             GameConfig.blockKinds.ITEM_LAMP,
    //         ], // 随机的话下面的不用写
    //         [GameConfig.blockKinds.ITEM_HEART]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //         [GameConfig.blockKinds.ITEM_LAMP]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //     },

    //     // 目标类型：mine: 矿石，depth：深度，coin：金币
    //     // 每关可能有多个目标
    //     targets: [
    //         { kind: "mine", id: GameConfig.blockKinds.MINE_A20, count: 20 }, // 挖20个19号矿石
    //         { kind: "mine", id: GameConfig.blockKinds.MINE_A26, count: 8 }, // 挖22个20号矿石
    //         // { kind: "depth", count: 20 }, // 到达30层级
    //         // { kind: "coin", count: 200 }, // 赚取200金币
    //     ],
    //     // 奖励类型: coin: 金币, diamond: 钻石, key: 钥匙，card: 卡片碎片，exp: 经验值'
    //     rewards: [
    //         // { kind: "coin", count: 10 },
    //         // { kind: "diamond", count: 3 },
    //         { kind: "key", count: 1 }, // 钥匙和宝箱配对，分三个等级的钥匙
    //         // {kind: "cardPart", id: 1_01, count: 1}, // 卡片碎片，id为卡片编号
    //         // {kind: "card", id: 1_01, count: 1}, // 卡片，id为卡片编号
    //         { kind: "exp", count: 600 }, // 奖励经验值
    //     ]
    // },
    // // 10
    // {
    //     // 地图配置，不能修改
    //     name: "沙漠",
    //     mapFile: "maps/desert/dwarf01", // 对应的地图文件
    //     bgFrame01: "chapterBg1TopFrame", // 对应Bg中配置背景图片
    //     bgFrame02: "chapterBg1BottomFrame",
    //     bgColor: "#25181f",
    //     trapRockFrame: "trapDwarfRockFrame", // Map中配置的图片
    //     trapSpikeFrame: "trapDesetSpikeFrame",
    //     itemFrame: "itemDwarfBucketFrame", // 问号砖块，未知道具

    //     // 下面为关卡配置
    //     random: false, // 需不需要随机，不论需不需要随机，下面的配置都要写，只是有的不用写，具体看注释，没说明的即使随机启用也不会影响

    //     itemHeart: 1, // 问号砖块，道具加血的血量
    //     itemLamp: 150, // 问号砖块，道具加矿灯的时长

    //     expMineDensity: 0.1, // 经验值矿石密度
    //     glowingTileDensity: 0.00001, // 发光砖块密度
    //     itemKeyChance: 0.5, // 发光砖块，挖土出现钥匙几率
    //     itemDiamondChance: 0.2, // 发光砖块，挖土出现卡片碎片几率
    //     itemHiddenMoney: 5, // 发光砖块，金币数量，注意不是概率，如果上面三个不出，这个保底出金币

    //     dirtHP: 4, // 土和发光砖块（其实也是土）的血量
    //     itemHP: 4, // 问号砖块血量
    //     mineBaseHP: 6, // 矿石血量，包括经验值矿石
    //     rockHP: 40, // 石头血量

    //     damages: { // 陷阱造成的伤害
    //         [GameConfig.blockKinds.TRAP_SPIKE]: 2,
    //         [GameConfig.blockKinds.TRAP_ROCK]: 2,
    //     },
    //     biome: { // 如果随机，这里写的会被修改
    //         count: 0, // 可以设到100
    //         size: 1, // 1, 2, 3: 依次最大，真实大小是随机的，每种尺寸有个范围
    //     },
    //     exits: {
    //         ladderTileID: 42, // 这几个是自动生成出口的关键tile id，可以在地图上点出来后，打开地图的tmx文件查看ID
    //     },
    //     mines: { // 矿石配置
    //         kinds: [GameConfig.blockKinds.MINE_A20], // 随机的话下面的不用写，否则必须写
    //         // 具体配置格式参考:
    //         [GameConfig.blockKinds.MINE_A20]: {
    //             minLevel: 1,
    //             maxLevel: 250,
    //             density: 0.08, //0-1, 1表示每个tile都会有
    //         },
    //     },
    //     traps: {
    //         kinds: [GameConfig.blockKinds.TRAP_SPIKE, GameConfig.blockKinds.TRAP_ROCK],// 随机的话下面的不用写
    //         [GameConfig.blockKinds.TRAP_SPIKE]: {
    //             minLevel: 5,
    //             maxLevel: 250,
    //             density: 0.04,
    //         },
    //         [GameConfig.blockKinds.TRAP_ROCK]: {
    //             minLevel: 3,
    //             maxLevel: 250,
    //             density: 0.04,
    //         },
    //     },
    //     items: {
    //         kinds: [
    //             GameConfig.blockKinds.ITEM_HEART,
    //             GameConfig.blockKinds.ITEM_LAMP,
    //             GameConfig.blockKinds.ITEM_BOMB_FLYING,
    //             GameConfig.blockKinds.ITEM_BOMB_GRENADE,
    //             GameConfig.blockKinds.ITEM_BOMB_ZONE,
    //         ], // 随机的话下面的不用写
    //         [GameConfig.blockKinds.ITEM_HEART]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //         [GameConfig.blockKinds.ITEM_LAMP]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //         [GameConfig.blockKinds.ITEM_BOMB_FLYING]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //         [GameConfig.blockKinds.ITEM_BOMB_GRENADE]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //         [GameConfig.blockKinds.ITEM_BOMB_ZONE]: {
    //             minLevel: 2,
    //             maxLevel: 250,
    //             density: 0.02,
    //         },
    //     },

    //     // 目标类型：mine: 矿石，depth：深度，coin：金币
    //     // 每关可能有多个目标
    //     targets: [
    //         // { kind: "mine", id: GameConfig.blockKinds.MINE_A19, count: 20 }, // 挖20个19号矿石
    //         // { kind: "mine", id: GameConfig.blockKinds.MINE_A20, count: 22 }, // 挖22个20号矿石
    //         { kind: "depth", count: 50 }, // 到达30层级
    //         { kind: "coin", count: 30 }, // 赚取200金币
    //     ],
    //     // 奖励类型: coin: 金币, diamond: 钻石, key: 钥匙，card: 卡片碎片，exp: 经验值'
    //     rewards: [
    //         // { kind: "coin", count: 10 },
    //         { kind: "diamond", count: 5 },
    //         { kind: "key", count: 2 }, // 钥匙和宝箱配对，分三个等级的钥匙
    //         // {kind: "cardPart", id: 1_01, count: 1}, // 卡片碎片，id为卡片编号
    //         // {kind: "card", id: 1_01, count: 1}, // 卡片，id为卡片编号
    //         { kind: "exp", count: 500 }, // 奖励经验值
    //     ]
    // },
];
