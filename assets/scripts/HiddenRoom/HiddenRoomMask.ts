import { _decorator, Color, Component, director, Graphics, Tween, tween, UITransform, view, Vec3 } from 'cc';
import { EventBus } from '../EventBus';
import { GameConfig } from '../GameConfig';
import { Map } from '../Map';
import { Player } from '../Player';
import { HiddenMap } from './HiddenMap';

const { ccclass } = _decorator;

@ccclass('HiddenRoomMask')
export class HiddenRoomMask extends Component {
    public currentRadius: number = 0; // 初始半径，动画开始前会设置
    public maskColor = new Color().fromHEX('#111111'); // 黑色遮罩
    public isAnimationReady: boolean = false; // 标识动画是否已准备好

    private g: Graphics;
    private player: Player;
    private animationCallback: Function = null;
    private focusPosition: Vec3 = new Vec3();
    private animationTween: Tween<any> = null;

    start() {
        let winSize = view.getVisibleSize();

        this.g = this.node.getComponent(Graphics);

        // 底色 - 黑色遮罩背景
        let cover = this.node.getChildByName('Cover');
        if (cover) {
            let coverG = cover.getComponent(Graphics);
            cover.getComponent(UITransform).setContentSize(winSize);
            coverG.fillColor = this.maskColor;
            coverG.fillRect(-winSize.width / 2, -winSize.height / 2, winSize.width, winSize.height);
        } else {
            console.warn(`[HiddenRoomMask] 找不到Cover子节点`);
        }

        // 获取玩家节点
        const playerNode = director.getScene().getChildByPath('Canvas/TiledMap/Player');
        if (playerNode) {
            this.player = playerNode.getComponent(Player);
        } else {
            console.warn('HiddenRoomMask: Player node not found during initialization');
        }

        this.node.getComponent(UITransform).priority = GameConfig.UIPriority.lampMask;
    }

    protected onDestroy(): void {
        EventBus.targetOff(this);
        // 停止动画
        if (this.animationTween) {
            this.animationTween.stop();
            this.animationTween = null;
        }
    }

    update(_deltaTime: number) {
        // 如果玩家节点不存在，尝试重新获取
        if (!this.player) {
            const playerNode = director.getScene().getChildByPath('Canvas/TiledMap/Player');
            if (playerNode) {
                this.player = playerNode.getComponent(Player);
            } else {
                // 如果还是找不到玩家节点，使用焦点位置绘制一个简单的圆形遮罩
                this.drawMask(this.focusPosition);
                return;
            }
        }

        // 获取玩家当前位置作为焦点（或使用设定的焦点位置）
        let focus: Vec3;
        if (this.focusPosition.equals(Vec3.ZERO)) {
            const map = this.player.node.parent.getComponent(Map)
                ? this.player.node.parent.getComponent(Map)
                : this.player.node.parent.getComponent(HiddenMap);
            focus = map.convertChildPositionToUI(this.player.node);
        } else {
            focus = this.focusPosition;
        }

        // 绘制遮罩
        this.drawMask(focus);
    }

    /**
     * 绘制遮罩效果 - 按照GameoverMask的方式绘制圆形镂空
     */
    private drawMask(center: Vec3) {
        if (!this.g) {
            // 尝试重新获取Graphics组件
            this.g = this.node.getComponent(Graphics);
        }

        // 清除之前的绘制
        this.g.clear();

        // 绘制圆形镂空（与GameoverMask相同的方式）
        this.g.circle(center.x, center.y, this.currentRadius);
        this.g.fill();
    }

    /**
     * 开始进入隐藏房间的动画
     * @param focusPos 焦点位置（UI坐标系）
     * @param onComplete 动画完成回调
     */
    public startEnterAnimation(focusPos: Vec3, onComplete?: Function) {
        // 激活遮罩节点，开始显示
        this.node.active = true;

        // 重新尝试获取Graphics组件（可能start方法还没执行）
        if (!this.g) {
            this.g = this.node.getComponent(Graphics);
        }

        // 设置焦点位置
        this.focusPosition = focusPos.clone();
        this.animationCallback = onComplete;

        // 从较大半径（足够覆盖屏幕）缩小到0
        // const winSize = view.getVisibleSize();
        const maxRadius = 1000;
        this.currentRadius = maxRadius;

        // 立即绘制一次初始状态
        this.drawMask(this.focusPosition);

        // 获取玩家节点进行缩放动画
        const playerNode = director.getScene().getChildByPath('Canvas/TiledMap/Player');
        if (playerNode) {
            // 分阶段动画：先缩小遮罩到100，再同时缩小遮罩和玩家
            const animationTarget = { radius: this.currentRadius, playerScale: 1.0 };
            let callbackTriggered = false; // 确保回调只触发一次
            let playerScaleStarted = false; // 标记玩家缩放是否已开始

            this.animationTween = tween(animationTarget)
                .to(1.2, { radius: 0, playerScale: 0 }, {
                    onUpdate: (target: any) => {
                        this.currentRadius = target.radius;

                        // 当半径缩小到50时，开始缩放玩家
                        if (this.currentRadius <= 50 && !playerScaleStarted) {
                            playerScaleStarted = true;
                        }

                        // 只有在半径小于等于50时才缩放玩家
                        if (playerScaleStarted) {
                            const scale = target.playerScale;
                            playerNode.setScale(scale, scale, scale);
                        }

                        // 当半径缩小到0时，触发场景切换回调
                        if (this.currentRadius <= 0 && !callbackTriggered && this.animationCallback) {
                            callbackTriggered = true;
                            this.animationCallback();
                        }
                    }
                })
                .call(() => {
                    // 确保玩家最终缩放为0（完全隐藏）
                    if (playerScaleStarted) {
                        playerNode.setScale(0, 0, 0);
                    }
                    // 如果由于某种原因回调还没触发，这里触发一次
                    if (!callbackTriggered && this.animationCallback) {
                        this.animationCallback();
                    }
                })
                .start();
        } else {
            // 如果找不到玩家节点，只执行遮罩动画
            const animationTarget = { radius: this.currentRadius };
            let callbackTriggered = false; // 确保回调只触发一次

            this.animationTween = tween(animationTarget)
                .to(1.2, { radius: 0 }, {
                    onUpdate: (target: any) => {
                        this.currentRadius = target.radius;

                        // 当半径缩小到0时，触发场景切换回调
                        if (this.currentRadius <= 0 && !callbackTriggered && this.animationCallback) {
                            callbackTriggered = true;
                            this.animationCallback();
                        }
                    }
                })
                .call(() => {
                    // 如果由于某种原因回调还没触发，这里触发一次
                    if (!callbackTriggered && this.animationCallback) {
                        this.animationCallback();
                    }
                })
                .start();
        }
    }

    /**
     * 玩家出现动画（通用）- 从小变大出现
     * @param focusPos 焦点位置（UI坐标系）
     * @param onComplete 动画完成回调
     */
    public startAppearAnimation(focusPos: Vec3, onComplete?: Function) {
        // 激活遮罩节点，开始显示
        this.node.active = true;

        // 设置焦点位置
        this.focusPosition = focusPos.clone();
        this.animationCallback = onComplete;

        // 从小半径扩大到足够覆盖整个屏幕，然后消失
        this.currentRadius = 50; // 从较小半径开始

        // 获取玩家节点进行缩放动画 - 先尝试隐藏房间路径，再尝试主场景路径
        let playerNode = director.getScene().getChildByPath('Canvas/HiddenMap/Player');
        if (!playerNode) {
            playerNode = director.getScene().getChildByPath('Canvas/TiledMap/Player');
        }

        if (playerNode) {
            // 设置玩家初始缩放为0.1
            playerNode.setScale(0.1, 0.1, 0.1);

            const winSize = view.getVisibleSize();
            const maxRadius = Math.max(winSize.width, winSize.height);

            // 同时进行遮罩扩大和玩家放大动画
            const animationTarget = { radius: this.currentRadius, playerScale: 0.1 };
            this.animationTween = tween(animationTarget)
                .to(1.0, { radius: maxRadius, playerScale: 1.0 }, {
                    onUpdate: (target: any) => {
                        this.currentRadius = target.radius;
                        // 同步缩放玩家节点
                        const scale = target.playerScale;
                        playerNode.setScale(scale, scale, scale);
                    }
                })
                .call(() => {
                    // 确保玩家缩放为正常大小
                    playerNode.setScale(1, 1, 1);
                    if (this.animationCallback) {
                        this.animationCallback();
                    }
                    // 销毁遮罩节点
                    this.node.destroy();
                })
                .start();
        } else {
            const winSize = view.getVisibleSize();
            const maxRadius = Math.max(winSize.width, winSize.height);

            // 如果找不到玩家节点，只执行遮罩动画
            const animationTarget = { radius: this.currentRadius };
            this.animationTween = tween(animationTarget)
                .to(1.0, { radius: maxRadius }, {
                    onUpdate: (target: any) => {
                        this.currentRadius = target.radius;
                    }
                })
                .call(() => {
                    if (this.animationCallback) {
                        this.animationCallback();
                    }
                    // 销毁遮罩节点
                    this.node.destroy();
                })
                .start();
        }
    }
}
