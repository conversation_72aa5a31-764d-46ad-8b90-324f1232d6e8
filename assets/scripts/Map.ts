import {
    _decorator,
    Component,
    director,
    instantiate,
    math,
    Node,
    Prefab,
    <PERSON>ze,
    <PERSON>p<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    T<PERSON><PERSON><PERSON>er,
    TiledMap,
    tween,
    UITransform,
    Vec3,
    // view,
    assetManager,
} from 'cc';
import { EventBus } from './EventBus';
import { MapUtils } from './Utils/MapUtils';
import { GameConfig } from './GameConfig';
import { GameData } from './GameData';
import { Item } from './Item';
import { LevelGenerator } from './LevelGenerator';
import { Mine } from './Mine';
import { Player } from './Player';
import { Smoke } from './Smoke';
import { UserData } from './Utils/UserData';
import { AdMobRewardedVideo } from './AdMob/AdMobRewardedVideo';
import { HiddenRoomMask } from './HiddenRoom/HiddenRoomMask';
import { HiddenRoomStateManager } from './Utils/HiddenRoomStateManager';
import { WECHAT } from 'cc/env';
import { Saw } from './Saw';
import { RoundBlade } from './RoundBlade';
import { TwinBlade } from './TwinBlade';
import { Chapters } from './Chapters/Chapters';
import { CommonAssetsCtl } from './v1/Common/CommonAssetsCtl';
import { saveUserData, loadUserData } from './Utils/Tools';
import { FeverChapters } from './FeverChapters/FeverChapters';
import { sys } from 'cc';
import { game } from 'cc';
import { Chapters01 } from './Chapters/Chapter01';
import { BackgroundVertical } from './v1/Common/BackgroundVertical';
import { ExitDoor } from './HiddenRoom/ExitDoor';
const { ccclass, property } = _decorator;

@ccclass('Map')
export class Map extends Component {
    @property(Prefab)
    playerFab: Prefab;
    @property(Prefab)
    scarFab: Prefab;
    @property(SpriteFrame)
    nextMinePortalFrame: SpriteFrame;
    @property(Prefab)
    playerBuffPrefab: Prefab; // 玩家buff

    @property(Prefab)
    public mine01Fab: Prefab;

    @property(Prefab)
    public trap01Fab: Prefab;

    @property(Prefab)
    public bombFab: Prefab;
    @property(Prefab)
    public bombFlyingFab: Prefab;
    @property(Prefab)
    public bombGrenadeFab: Prefab;

    @property(Prefab)
    rockFallFab: Prefab;
    @property(Prefab)
    rockFall2Fab: Prefab;
    @property(Prefab)
    smokeFab: Prefab;
    @property(Prefab)
    glowingTileFab: Prefab;

    @property(Prefab)
    oneCardFab: Prefab;

    @property(Prefab)
    startBtnFab: Prefab;
    @property(SpriteFrame)
    dirtFrame: SpriteFrame;
    @property(SpriteFrame)
    adFrame: SpriteFrame;
    @property(Prefab)
    mineHelpFab: Prefab;
    @property(Prefab)
    sawFab: Prefab;
    @property(Prefab)
    roundBladeFab: Prefab;
    @property(Prefab)
    twinBladeFab: Prefab;

    @property(Prefab)
    itemFloatUpPrefab: Prefab;
    @property(Prefab)
    playerHurtPrafab: Prefab;

    @property(Prefab)
    mapBlockNodePrefab: Prefab;

    @property(Prefab)
    hiddenRoomEntranceFab: Prefab;

    @property(Prefab)
    hiddenRoomMaskFab: Prefab;

    private map: TiledMap;
    public mapSize: Size;
    public tileSize: Size;
    private mapWidth;
    // private mapHeight;
    // private winSize: Size;
    private mainCamera: Node;
    private cameraTargetPos: Vec3 = null;
    private uiCamera: Node;
    private rootNode: Node;
    public player: Node;
    public groundLayer: TiledLayer;
    public itemsLayer: TiledLayer;

    // private mapPlayingTilesSize = null;
    public tileGIDMaxY = GameConfig.mapMaxLevel - 2; // 不包含
    public mapBlocks = []; // 生成地图
    // public mineBlocks = []; // []，Node, 矿石
    public blocks = []; // Node, 矿石，敌人(目前没有)等等
    // GIDBlocks eg. [{kind: MINE_A19, initialHP: xx, subKind: xx, state: GameConfig.mineState.IN_DIRT, gid: vec3, isCreated: false}...]
    // public GIDBlocks = []; // 整张地图全部经验石、矿石、问号、陷阱（石头，地刺）
    // public nodeBlocks = []; // Node， GIDBlocks
    public trapRocks = []; // Node， 再存一份石头的数据，用于提升性能，因此这个性能更好
    // GIDTrapRocks eg. [{kind: TRAP_ROCK, initialHP: xx, subKind: xx, state: GameConfig.mineState.IN_DIRT, gid: vec3, isCreated: false}...]
    // public nodeTrapRocks = []; // Node， 再存一份石头的数据，用于提升性能，因此这个性能更好
    public scars = []; // Node, 裂痕，只有没有blocks的地方有
    private exitGIDs = []; // gid,  vec3
    public glowingTiles = []; // Node, 隐藏的会发光的道具的坐标，在土里面，挖出后显示，这个上面不会有blocks
    // GIDGlowingTiles eg.[gid, gid, gid...]
    // public GIDGlowingTiles = []; // 整张地图全部隐藏的会发光的道具的坐标，在土里面，挖出后显示，这个上面不会有blocks

    private mainCameraState = GameConfig.cameraStates.FOLLOWING;

    private tmpPos = new Vec3(0, 0, 0);
    public targetDepth = -1; // 不等于-1表示本关目标是层数不是矿石，只能二选一，但是可以是多个矿石目标
    public treasureGID: Vec3 = new Vec3(); // 宝箱的gid
    public treasureNode: Node = null;
    public commonAssets: CommonAssetsCtl;

    public adService: AdMobRewardedVideo = null;

    private spawnedLevels = {}; // 记录已经生成的层数, {y: true}
    private bgScript: BackgroundVertical;
    private timePassed = 0;

    protected onLoad(): void {
        GameConfig.currentGameState = null;
        GameData.mode = UserData.nextMode;
        UserData.nextMode = 'general';
        saveUserData();

        game.frameRate = 60;
    }

    onDestroy() {
        // 销毁矿石节点
        this.blocks.forEach(block => block.destroy());
        this.blocks = [];

        // 销毁陷阱节点
        this.trapRocks.forEach(trap => trap.destroy());
        this.trapRocks = [];

        // 销毁发光道具节点
        this.glowingTiles.forEach(tile => tile.destroy());
        this.glowingTiles = [];

        // 销毁裂痕节点
        this.scars.forEach(scar => scar.destroy());
        this.scars = [];

        // 销毁宝箱节点
        if (this.treasureNode) {
            this.treasureNode.destroy();
            this.treasureNode = null;
        }

        // 释放地图资源
        if (this.map && this.map.tmxAsset) {
            assetManager.releaseAsset(this.map.tmxAsset);
            this.map.tmxAsset = null;
        }
    }

    start() {
        // 确保数据已加载
        loadUserData();

        this.commonAssets = director
            .getScene()
            .getChildByPath('Canvas/CommonAssets')
            .getComponent(CommonAssetsCtl);
        EventBus.emit('playSound', 'play2');

        let res = JSON.parse(
            JSON.stringify(Chapters[UserData.currentChapter - 1][UserData.currentLevel - 1])
        ); // clone
        if (GameData.mode === 'fever') {
            res = JSON.parse(JSON.stringify(FeverChapters[UserData.currentChapter - 1])); // clone
        }
        if (!GameData.levelGenerator) GameData.levelGenerator = new LevelGenerator();
        GameData.currentLevelConfig = GameData.levelGenerator.generateLevel({
            luck: 1, // 玩家等级
            res: res,
            totalLevels: this.tileGIDMaxY,
        });
        if (GameData.mode === 'general') {
            GameData.currentLevelTargetData = GameData.currentLevelConfig.targets.map((item: any) => ({
                ...item,
                currentCount: 0,
            }));
            let depthConfig = GameData.currentLevelTargetData.find((v: any) => v.kind == 'depth');
            if (depthConfig) {
                this.targetDepth = depthConfig.count;
            }
        } else {
            this.targetDepth = GameConfig.mapMaxLevel;
        }

        // 添加卡片到卡片池 并去重
        GameData.cardsPool = [...new Set([...GameData.cardsPool, ...UserData.cards])];

        // this.winSize = view.getVisibleSize();

        this.mainCamera = director.getScene().getChildByPath('Canvas/MainCamera');
        this.uiCamera = director.getScene().getChildByPath('Canvas/UICamera');
        this.rootNode = director.getScene().getChildByPath('Canvas');

        // let gs = this.map.getObjectGroups();
        this.mapSize = new Size(11, GameConfig.mapMaxLevel); // 对应地图11格152行
        this.tileSize = new Size(64, 64); // 每个格子64*64
        this.mapWidth = this.mapSize.width * this.tileSize.width;
        // this.mapHeight = this.mapSize.height * this.tileSize.height;
        // this.mapPlayingTilesSize = this.mapSize.width - 2 * 2; // 左右两边2格

        // 地图目前放大了1.2倍(在TiledMap节点上配置)，所以这里定位时要考虑到缩放
        this.node.setPosition(new Vec3((-this.mapWidth * this.node.scale.x) / 2, 100, 0));

        // bg color
        // let totalHeight = this.winSize.height + this.mapHeight;
        this.bgScript = director
            .getScene()
            .getChildByPath('Canvas/Bg2')
            .getComponent(BackgroundVertical);

        // 生成自动肉鸽类型的地图
        let playerGID = new Vec3(2, 0, 0);
        this.SpawnRoguelikeMaps(playerGID);
        // 也可以手工定制生成地图

        // 生成玩家
        this.player = instantiate(this.playerFab);
        this.player.getComponent(UITransform).priority = GameConfig.UIPriority.player;
        this.player.setSiblingIndex(GameConfig.UIPriority.player);
        let playerPos = MapUtils.getLocationByGID(playerGID);
        playerPos.y += GameConfig.tilePlayerPosYOffset;
        this.player.setPosition(playerPos);

        this.node.addChild(this.player);

        // 检查是否从隐藏房间返回，如果是，则调整玩家位置为隐藏房间入口处
        const savedEntranceGID = HiddenRoomStateManager.getCurrentEntranceGID();
        if (savedEntranceGID) {
            console.log(`[MAP] 检测到从隐藏房间返回，调整玩家位置到入口: (${savedEntranceGID.x}, ${savedEntranceGID.y})`);
            const entrancePlayerPos = MapUtils.getLocationByGID(new Vec3(savedEntranceGID.x, savedEntranceGID.y, 0));
            entrancePlayerPos.y += GameConfig.tilePlayerPosYOffset;
            this.player.setPosition(entrancePlayerPos);

            // 立即清除保存的入口GID，避免时序冲突
            HiddenRoomStateManager.clearCurrentEntranceGID();
            console.log(`[MAP] 玩家位置已调整完成，已清除保存的入口GID`);

            // 播放玩家出现在主场景的动画
            this.createAppearInMainSceneAnimation();
        }

        if (GameData.mode === 'general') {
            EventBus.emit('loadGameMask');
        }

        if (WECHAT) {
        } else {
            this.adService = AdMobRewardedVideo.getInstance();
            this.adService.preloadAd();
        }
    }

    public SpawnRoguelikeMaps(playerGID: Vec3) {
        let minimalX = 2; // 包含
        let maxX = this.mapSize.width - 2; // 不包含
        let minimalY = 1; // 始终从地表开始，确保完整地图生成
        let maxY = playerGID.y + GameConfig.maxUpdateLevelsRange; // 不包含

        let getChance = ({ minLevel, maxLevel, density }) => {
            let totalTiles = (maxLevel - minLevel) * (maxX - minimalX);
            let avgChance = (totalTiles * density) / totalTiles;
            return avgChance;
        };
        let setupTile = ({ b, existRowData, newRowData, tileFrame }) => {
            if (existRowData) {
                if (existRowData.tile != -1) {
                    b.getComponent(Sprite).spriteFrame = this.commonAssets[existRowData.tile];
                    b.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 2;
                    this.scheduleOnce(() => {
                        b.setSiblingIndex(GameConfig.UIPriority.allUI - 2);
                    }, 0);
                    this.node.addChild(b);
                    this.mapBlocks.push(b);
                }
            } else {
                newRowData['tile'] = tileFrame;
                b.getComponent(Sprite).spriteFrame = this.commonAssets[newRowData['tile']];
                b.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 2;
                this.scheduleOnce(() => {
                    b.setSiblingIndex(GameConfig.UIPriority.allUI - 2);
                }, 0);
                this.node.addChild(b);
                this.mapBlocks.push(b);
            }
        };

        let counters = {};
        // ***************
        // Mines and Items and Traps
        for (let y = minimalY; y <= maxY; y++) {
            if (this.spawnedLevels[y]) continue; // 已经生成过
            this.spawnedLevels[y] = true;

            // 章节配置
            let chapConfig = null;
            let chapIndex = -1;
            for (let i = 0; i < Chapters01.length; i++) {
                if (y >= Chapters01[i]['levels'][0] && y <= Chapters01[i]['levels'][1]) {
                    chapIndex = i;
                    chapConfig = Chapters01[i];
                    break;
                }
            }
            if (!chapConfig) {
                console.warn(`No chapter config found for level ${y}`);
                continue;
            }

            for (let x = 1; x <= 9; x++) {
                // 需要画的地图块X范围

                // 保存的数据
                let memoKey = `${y}:${x}`;
                let existRowData = UserData.levelMemo[memoKey];
                let newRowData = {};

                // 加载地图块
                let b = instantiate(this.mapBlockNodePrefab);
                if (y === 1) {
                    let bPos = MapUtils.getLocationByGID(new Vec3(x, 1, 0));
                    // 第一层, 第一层永远需要生成，所以传递existRowData为false
                    switch (x) {
                        case 1:
                            setupTile({
                                b,
                                existRowData: false,
                                newRowData,
                                tileFrame: `chapter${chapIndex + 1}MapTopLeftFrame`,
                            });
                            break;
                        case 9:
                            setupTile({
                                b,
                                existRowData: false,
                                newRowData,
                                tileFrame: `chapter${chapIndex + 1}MapTopRightFrame`,
                            });
                            break;
                        default:
                            setupTile({
                                b,
                                existRowData: false,
                                newRowData,
                                tileFrame: `chapter${chapIndex + 1}MapTopCenterFrame`,
                            });
                            break;
                    }
                    b.setPosition(bPos);
                } else if (y === GameConfig.mapMaxLevel + 1) {
                    // 最后一层
                    let bPos = MapUtils.getLocationByGID(new Vec3(x, y, 0));
                    switch (x) {
                        case 1:
                            setupTile({
                                b,
                                existRowData,
                                newRowData,
                                tileFrame: `chapter${chapIndex + 1}MapBottomLeftFrame`,
                            });
                            break;
                        case 9:
                            setupTile({
                                b,
                                existRowData,
                                newRowData,
                                tileFrame: `chapter${chapIndex + 1}MapBottomRightFrame`,
                            });
                            break;
                        default:
                            setupTile({
                                b,
                                existRowData,
                                newRowData,
                                tileFrame: `chapter${chapIndex + 1}MapBottomCenterFrame`,
                            });
                            break;
                    }
                    b.setPosition(bPos);
                } else {
                    // 中间层
                    let bPos = MapUtils.getLocationByGID(new Vec3(x, y, 0));
                    switch (x) {
                        case 1:
                            setupTile({
                                b,
                                existRowData,
                                newRowData,
                                tileFrame: `chapter${chapIndex + 1}MapMiddleLeftFrame`,
                            });
                            break;
                        case 9:
                            setupTile({
                                b,
                                existRowData,
                                newRowData,
                                tileFrame: `chapter${chapIndex + 1}MapMiddleRightFrame`,
                            });
                            break;
                        default:
                            setupTile({
                                b,
                                existRowData,
                                newRowData,
                                tileFrame: `chapter${chapIndex + 1}MapMiddleCenterFrame`,
                            });
                            break;
                    }
                    b.setPosition(bPos);
                }
                // this.saveLevelMemo(memoKey, newRowData);

                // 地图内容
                if (x >= 2 && x <= 8 && y >= 2) {

                    if (existRowData) {
                        // 特殊处理：检查是否是隐藏房间入口位置
                        if (chapConfig.hiddenRooms && chapConfig.hiddenRooms.entranceGID) {
                            const entranceGID = chapConfig.hiddenRooms.entranceGID;
                            if (x === entranceGID[0] && y === entranceGID[1]) {
                                const entranceGIDObj = { x: entranceGID[0], y: entranceGID[1] };
                                const isEntranceDug =
                                    HiddenRoomStateManager.isEntranceDug(entranceGIDObj);



                                if (isEntranceDug) {
                                    // 已挖掘，使用类似 plantHiddenRoom 的逻辑生成入口
                                    this.tmpPos.x = x;
                                    this.tmpPos.y = y;
                                    this.tmpPos.z = 0;
                                    let pos = MapUtils.getLocationByGID(this.tmpPos);

                                    if (this.hiddenRoomEntranceFab) {
                                        const entrance = instantiate(this.hiddenRoomEntranceFab);
                                        entrance.setPosition(pos);

                                        // 应用类似 plantHiddenRoom 的设置
                                        entrance.setScale(new Vec3(0.25, 0.25, 1));
                                        entrance.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
                                        entrance.setSiblingIndex(GameConfig.UIPriority.allUI - 1);

                                        // 添加并配置 ExitDoor 组件
                                        let exitDoorComponent = entrance.getComponent(ExitDoor);
                                        if (!exitDoorComponent) {
                                            exitDoorComponent = entrance.addComponent(ExitDoor);
                                        }
                                        exitDoorComponent.isExit = false; // 设置为入口而非出口

                                        this.node.addChild(entrance);
                                    }
                                    continue;
                                } else {
                                    // 未挖掘，按照存储的数据生成（可能是隐藏房间入口道具）
                                    console.log(
                                        `[MAP_ENTRANCE_DEBUG] 隐藏房间入口未挖掘，按存储数据生成`
                                    );
                                }
                            }
                        }

                        if (existRowData['tile'] == -1 || existRowData['block'] == -1) {
                            // tile没有的话不生成内容，也就是挖出来后不捡就没有了
                            continue;
                        } else if (existRowData['block'] && existRowData['block'] != -1) {
                            // 读取老数据
                            this.tmpPos.x = x;
                            this.tmpPos.y = y;
                            this.tmpPos.z = 0;
                            let pos = MapUtils.getLocationByGID(this.tmpPos);

                            if (existRowData['block'].type == 'expMine') {
                                counters[GameConfig.blockKinds.MINE_A19] ||= 0;
                                counters[GameConfig.blockKinds.MINE_A19] += 1;

                                let mine = instantiate(this.mine01Fab);
                                let ms = mine.getComponent(Mine) as Mine;
                                ms.kind = GameConfig.blockKinds.MINE_A19;
                                ms.initialHP = chapConfig.mineBaseHP;
                                mine.getComponent(UITransform).priority =
                                    GameConfig.UIPriority.allUI - 1;
                                mine.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
                                mine.setPosition(pos);
                                this.node.addChild(mine);
                                this.blocks.push(mine);
                            } else if (existRowData['block'].type == 'glowingTile') {
                                counters[GameConfig.blockKinds.MINE_A18] ||= 0;
                                counters[GameConfig.blockKinds.MINE_A18] += 1;

                                let glowing = instantiate(this.glowingTileFab);
                                glowing.setPosition(pos);
                                glowing.getComponent(UITransform).priority =
                                    GameConfig.UIPriority.allUI - 1;
                                glowing.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
                                this.node.addChild(glowing);
                                this.glowingTiles.push(glowing);
                            } else if (existRowData['block'].type == 'mine') {
                                let mine = instantiate(this.mine01Fab);
                                let ms = mine.getComponent(Mine) as Mine;
                                ms.kind = existRowData['block'].kind;
                                ms.initialHP = existRowData['block'].hp;
                                mine.setPosition(pos);
                                mine.getComponent(UITransform).priority =
                                    GameConfig.UIPriority.allUI - 1;
                                mine.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
                                this.node.addChild(mine);
                                this.blocks.push(mine);
                            } else if (existRowData['block'].type == 'item') {
                                counters[existRowData['block'].kind] ||= 0;
                                counters[existRowData['block'].kind] += 1;
                                this.placeItemAt(pos, existRowData['block'].kind, chapConfig);
                            }
                        }
                    } else {
                        // 新生成
                        this.tmpPos.x = x;
                        this.tmpPos.y = y;
                        this.tmpPos.z = 0;
                        let gidUsed = false;
                        let pos = MapUtils.getLocationByGID(this.tmpPos);

                        // 经验值矿石，一种特殊的矿石
                        let chance = getChance({
                            minLevel: chapConfig.levels[0],
                            maxLevel: chapConfig.levels[1],
                            density: chapConfig.expMineDensity,
                        });
                        if (
                            y >= chapConfig.levels[0] &&
                            y <= chapConfig.levels[1] &&
                            math.random() < chance
                        ) {
                            counters[GameConfig.blockKinds.MINE_A19] ||= 0;
                            counters[GameConfig.blockKinds.MINE_A19] += 1;

                            let mine = instantiate(this.mine01Fab);
                            let ms = mine.getComponent(Mine) as Mine;
                            ms.kind = GameConfig.blockKinds.MINE_A19;
                            ms.initialHP = chapConfig.mineBaseHP;
                            mine.getComponent(UITransform).priority =
                                GameConfig.UIPriority.allUI - 1;
                            mine.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
                            mine.setPosition(pos);
                            this.node.addChild(mine);
                            this.blocks.push(mine);

                            gidUsed = true;
                            newRowData['block'] = {
                                type: 'expMine',
                                kind: GameConfig.blockKinds.MINE_A19,
                            };
                            // this.saveLevelMemo(memoKey, newRowData);
                            continue; // 已被占用
                        }

                        // 会发光的隐藏道具
                        chance = getChance({
                            minLevel: chapConfig.levels[0],
                            maxLevel: chapConfig.levels[1],
                            density: chapConfig.glowingTileDensity,
                        });
                        if (
                            y >= chapConfig.levels[0] &&
                            y <= chapConfig.levels[1] &&
                            math.random() < chance
                        ) {
                            counters[GameConfig.blockKinds.MINE_A18] ||= 0;
                            counters[GameConfig.blockKinds.MINE_A18] += 1;

                            let glowing = instantiate(this.glowingTileFab);
                            glowing.setPosition(pos);
                            glowing.getComponent(UITransform).priority =
                                GameConfig.UIPriority.allUI - 1;
                            glowing.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
                            this.node.addChild(glowing);
                            this.glowingTiles.push(glowing);

                            gidUsed = true;
                            newRowData['block'] = { type: 'glowingTile' };
                            // this.saveLevelMemo(memoKey, newRowData);
                            continue; // 已被占用
                        } // 隐藏房间入口
                        if (
                            !gidUsed &&
                            chapConfig.hiddenRooms &&
                            chapConfig.hiddenRooms.entranceGID
                        ) {
                            const entranceGID = chapConfig.hiddenRooms.entranceGID;
                            if (x === entranceGID[0] && y === entranceGID[1]) {
                                // 生成发光瓦片让土发光
                                let glowing = instantiate(this.glowingTileFab);
                                glowing.setPosition(pos);
                                glowing.getComponent(UITransform).priority =
                                    GameConfig.UIPriority.allUI - 1;
                                glowing.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
                                this.node.addChild(glowing);
                                this.glowingTiles.push(glowing);

                                // 生成隐藏房间入口道具（像其他道具一样）
                                this.placeItemAt(
                                    pos,
                                    GameConfig.blockKinds.ITEM_HIDDEN_ROOM_ENTRANCE,
                                    chapConfig
                                );

                                gidUsed = true;
                                newRowData['block'] = {
                                    type: 'item',
                                    kind: GameConfig.blockKinds.ITEM_HIDDEN_ROOM_ENTRANCE,
                                };

                                // this.saveLevelMemo(memoKey, newRowData);
                                continue; // 已被占用
                            }
                        }
                    }
                }
            }
        }

        // this.itemsLayer.markForUpdateRenderData(true);
        // 如果超过最大层数，删除已经生成的地图块
        if (playerGID.y > GameConfig.maxUpdateLevelsRange) {
            for (let i = 0; i < this.mapBlocks.length; i++) {
                let mb = this.mapBlocks[i];
                let mbGID = MapUtils.getGIDByLocation(mb.position);
                if (mbGID.y < playerGID.y - GameConfig.maxUpdateLevelsRange) {
                    mb.destroy();
                    this.mapBlocks.splice(i, 1);
                    i++;
                }
            }

            for (let i = 0; i < this.blocks.length; i++) {
                let mb = this.blocks[i];
                let mbGID = MapUtils.getGIDByLocation(mb.position);
                if (
                    mbGID.y < playerGID.y - GameConfig.maxUpdateLevelsRange || // 在玩家上面的
                    mbGID.y > playerGID.y + GameConfig.maxUpdateLevelsRange * 2 // 在玩家下面的，这种表示一直在往下掉
                ) {
                    mb.destroy();
                    this.blocks.splice(i, 1);
                    i++;
                }
            }

            for (let i = 0; i < this.glowingTiles.length; i++) {
                let mb = this.glowingTiles[i];
                let mbGID = MapUtils.getGIDByLocation(mb.position);
                if (mbGID.y < playerGID.y - GameConfig.maxUpdateLevelsRange) {
                    mb.destroy();
                    this.glowingTiles.splice(i, 1);
                    i++;
                }
            }

            for (let i = 0; i < this.scars.length; i++) {
                let mb = this.scars[i];
                let mbGID = MapUtils.getGIDByLocation(mb.position);
                if (mbGID.y < playerGID.y - GameConfig.maxUpdateLevelsRange) {
                    mb.destroy();
                    this.scars.splice(i, 1);
                    i++;
                }
            }
        }
    }

    public saveLevelMemo(key: string, v: any) {
        let existValue = UserData.levelMemo[key] || {};
        for (let k2 in v) {
            existValue[k2] = v[k2];
        }
        UserData.levelMemo[key] = existValue;
        sys.localStorage.setItem('userData', JSON.stringify(UserData));
    }

    public removeItemLayerTileAt(gid: Vec3, autoUpdate: boolean = true) {
        this.itemsLayer.setTileGIDAt(-1, gid.x, gid.y, 1);
        if (autoUpdate) {
            this.itemsLayer.markForUpdateRenderData(true); // 必须手工调用，否则地图不会更新
        }
    }

    public removeTileAt(gid: Vec3) {
        // this.groundLayer.setTileGIDAt(-1, gid.x, gid.y, 1);
        // if (autoUpdate) {
        //     this.groundLayer.markForUpdateRenderData(true); // 必须手工调用，否则地图不会更新
        // }
        for (let i = 0; i < this.mapBlocks.length; i++) {
            let b = this.mapBlocks[i];
            let bGID = MapUtils.getGIDByLocation(b.position);
            if (bGID.x === gid.x && bGID.y === gid.y) {
                b.destroy();
                this.mapBlocks.splice(i, 1);
                break;
            }
        }
    }

    public placeItemAt(pos: Vec3, subKind: number, chapConfig: any): [Node, Mine] {
        if (!chapConfig) {
            [chapConfig] = this.getChapConfigByGID(MapUtils.getGIDByLocation(pos));
        }
        let mine = instantiate(this.mine01Fab); // 也使用矿石的逻辑
        let ms = mine.getComponent(Mine) as Mine;
        ms.kind = GameConfig.blockKinds.ITEM;
        ms.initialHP = chapConfig.itemHP;
        ms.subKind = subKind;
        mine.setPosition(pos);
        mine.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        mine.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        this.node.addChild(mine);
        this.blocks.push(mine);

        return [mine, ms];
    }

    getChapConfigByGID(gid: Vec3): [any, number] {
        let chapConfig = null;
        let chapIndex = -1;
        for (let i = 0; i < Chapters01.length; i++) {
            if (gid.y >= Chapters01[i]['levels'][0] && gid.y <= Chapters01[i]['levels'][1]) {
                chapIndex = i;
                chapConfig = Chapters01[i];
                break;
            }
        }
        return [chapConfig, chapIndex];
    }

    // 先创建矿石，再获取
    public getBlockAtGID(gid: Vec3): [Node, number] {
        let block = null;
        let idx = -1;
        for (let i = 0; i < this.blocks.length; i++) {
            if (MapUtils.getGIDByLocation(this.blocks[i].position).equals(gid)) {
                idx = i;
                block = this.blocks[i];
                break;
            }
        }
        return [block, idx];
    }

    getGlowingTileAtGID(gid: Vec3): [Node, number] {
        let block = null;
        let idx = -1;
        for (let i = 0; i < this.glowingTiles.length; i++) {
            if (MapUtils.getGIDByLocation(this.glowingTiles[i].position).equals(gid)) {
                idx = i;
                block = this.glowingTiles[i];
                break;
            }
        }
        return [block, idx];
    }

    // 这个专门用来拿石头的，不从blocks中获取，提升性能
    getRockAtGID(gid: Vec3): [Node, number] {
        let rock = null;
        let idx = -1;
        for (let i = 0; i < this.trapRocks.length; i++) {
            if (MapUtils.getGIDByLocation(this.trapRocks[i].position).equals(gid)) {
                idx = i;
                rock = this.trapRocks[i];
                break;
            }
        }
        return [rock, idx];
    }

    markMineExposedAndRemoveItem(gid: Vec3) {
        let [b, idx] = this.getBlockAtGID(gid);
        if (idx > -1) {
            // 因为道具需要破坏才能出来，所以直接暂时删除
            if (b.getComponent(Mine).kind == GameConfig.blockKinds.ITEM) {
                b.destroy();
                this.blocks.splice(idx, 1);
            } else {
                // 矿石的话暴露出来
                b.getComponent(Mine).state = GameConfig.mineState.EXPOSED;
            }
        }

        // 放光矿石也需要删除掉
        let [glowing, idx2] = this.getGlowingTileAtGID(gid);
        if (idx2 > -1) {
            glowing.destroy();
            this.glowingTiles.splice(idx2, 1);
        }
    }

    public getScarAtGID(gid: Vec3): [Node, number] {
        let scar = null;
        let idx = -1;
        for (let i = 0; i < this.scars.length; i++) {
            if (MapUtils.getGIDByLocation(this.scars[i].position).equals(gid)) {
                idx = i;
                scar = this.scars[i];
                break;
            }
        }
        return [scar, idx];
    }

    public removeScarAtGID(gid: Vec3) {
        for (let i = 0; i < this.scars.length; i++) {
            if (MapUtils.getGIDByLocation(this.scars[i].position).equals(gid)) {
                this.scars[i].destroy();
                this.scars.splice(i, 1);
                return;
            }
        }
    }

    // 检查指定位置是否是隐藏房间入口
    public isHiddenRoomEntrance(gid: Vec3): boolean {
        const chapConfig = GameData.currentLevelConfig;
        if (!chapConfig.hiddenRooms || !chapConfig.hiddenRooms.entranceGID) {
            return false;
        }
        const entranceGID = chapConfig.hiddenRooms.entranceGID;
        return gid.x === entranceGID[0] && gid.y === entranceGID[1];
    }

    // 检查玩家是否在隐藏房间入口位置
    public isPlayerOnHiddenRoomEntrance(playerPos: Vec3): boolean {
        const playerGID = MapUtils.getGIDByLocation(playerPos);

        // 方法1：检查该位置是否有隐藏房间入口道具（未挖掘状态）
        for (let i = 0; i < this.blocks.length; i++) {
            let block = this.blocks[i];
            let blockGID = MapUtils.getGIDByLocation(block.position);
            if (blockGID.equals(playerGID)) {
                let mine = block.getComponent(Mine);
                if (
                    mine &&
                    mine.kind === GameConfig.blockKinds.ITEM &&
                    mine.subKind === GameConfig.blockKinds.ITEM_HIDDEN_ROOM_ENTRANCE
                ) {
                    return true;
                }
            }
        }

        // 方法2：检查该位置是否有隐藏房间门（已挖掘状态）
        // 遍历所有子节点寻找 ExitDoor 组件
        for (let i = 0; i < this.node.children.length; i++) {
            let child = this.node.children[i];
            let childGID = MapUtils.getGIDByLocation(child.position);
            if (childGID.equals(playerGID)) {
                let exitDoor = child.getComponent(ExitDoor);
                if (exitDoor && !exitDoor.isExit) { // isExit=false 表示是入口
                    console.log(`[MAP] 检测到玩家在隐藏房间门入口位置: (${playerGID.x}, ${playerGID.y})`);
                    return true;
                }
            }
        }

        return false;
    }

    // 是否在矿区内部，排除两侧的墙
    public isBetweenWall(gid: Vec3): boolean {
        if (!MapUtils.isGIDValid(gid, this.mapSize)) return false;
        if (gid.x <= 1 || gid.x >= this.mapSize.width - 2) return false;
        return true;
    }

    // 如果dir = "no"，表示直接获取传入坐标的tile类型
    // posType: pos | gid
    public getNearTileType(sourcePos: Vec3, dir: string, posType: string): [number, Vec3] {
        let targetGID;
        if (dir == 'no') {
            if (posType == 'pos') {
                targetGID = MapUtils.getGIDByLocation(sourcePos);
            } else {
                targetGID = sourcePos;
            }
        } else {
            if (posType == 'gid') {
                sourcePos = MapUtils.getLocationByGID(sourcePos);
            }
            targetGID = MapUtils.getNearGIDByLocation(sourcePos, dir, this.mapSize);
        }
        if (targetGID) {
            // 优先看是不是门

            for (let i = 0; i < this.exitGIDs.length; i++) {
                if (this.exitGIDs[i].equals(targetGID)) {
                    return [GameConfig.tileKinds.Exit, targetGID];
                }
            }

            let id = this.existInMapBlocks(targetGID.x, targetGID.y);
            // 前面两列和后面两列不能走
            if (targetGID.x <= 1 || targetGID.x >= this.mapSize.width - 2) {
                return [GameConfig.tileKinds.Edge, null];
            } else {
                // 没有tile
                if (id == 0) {
                    // 空的情况下要看上面是否有障碍物
                    let [_, idx] = this.getRockAtGID(targetGID);
                    if (idx > -1) {
                        return [GameConfig.tileKinds.Obstacle, targetGID];
                    } else {
                        return [GameConfig.tileKinds.Empty, targetGID];
                    }
                } else {
                    return [GameConfig.tileKinds.Dirt, targetGID];
                }
            }
        } else {
            return [GameConfig.tileKinds.Edge, null];
        }
    }

    // this.groundLayer.getTileGIDAt(targetGID.x, targetGID.y); 代替这个方法返回的是0，表示没有tile
    public existInMapBlocks(GIDX: number, GIDY: number): number {
        for (let i = 0; i < this.mapBlocks.length; i++) {
            let b = this.mapBlocks[i];
            let gid = MapUtils.getGIDByLocation(b.position);
            if (gid.x == GIDX && gid.y == GIDY) {
                return 1;
            }
        }
        return 0;
    }

    gidInPlayerRange(gid: Vec3): boolean {
        let playerGID = MapUtils.getGIDByLocation(this.player?.position);
        if (
            gid.y < playerGID.y - GameConfig.maxUpdateLevelsRange ||
            gid.y > playerGID.y + GameConfig.maxUpdateLevelsRange
        ) {
            return false;
        }
        return true;
    }

    public spawnRockFallAtGID(gid: Vec3) {
        if (!this.gidInPlayerRange(gid)) return;
        for (let i = 0; i < math.randomRangeInt(3, 6); i++) {
            let pos = MapUtils.getLocationByGID(gid);
            let s = instantiate(this.rockFallFab);
            pos.y += math.randomRange(-20, 20);
            pos.x += math.randomRange(-30, 30);
            s.setPosition(pos);
            s.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            s.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(s);
        }
    }

    public spawnRockFall2AtGID(gid: Vec3) {
        if (!this.gidInPlayerRange(gid)) return;
        let pos = MapUtils.getLocationByGID(gid);
        let s = instantiate(this.rockFall2Fab);
        s.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        s.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        s.setPosition(pos);
        this.node.addChild(s);
    }

    public spawnSmokeAtGID(gid: Vec3) {
        if (!this.gidInPlayerRange(gid)) return;
        let s = instantiate(this.smokeFab);
        let pos = MapUtils.getLocationByGID(gid);
        pos.y += math.randomRange(20, 40);
        pos.x += math.randomRange(-20, 20);
        s.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        s.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        s.setPosition(pos);
        this.node.addChild(s);
    }

    public spawnSawAtPos(pos: Vec3, level: number) {
        let gid = MapUtils.getGIDByLocation(pos);
        // left
        let saw = instantiate(this.sawFab);
        saw.setPosition(pos);
        saw.getComponent(Saw).flyingDir = 1;
        saw.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        saw.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        this.node.addChild(saw);
        // right
        let saw2 = instantiate(this.sawFab);
        saw2.setPosition(pos);
        saw2.getComponent(Saw).flyingDir = -1;
        saw2.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        saw2.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        this.node.addChild(saw2);

        if (level == 3) {
            let pos = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 1, 0));
            let saw = instantiate(this.sawFab);
            saw.setPosition(pos);
            saw.getComponent(Saw).flyingDir = 1;
            saw.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw);
            let pos2 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 1, 0));
            let saw2 = instantiate(this.sawFab);
            saw2.setPosition(pos2);
            saw2.getComponent(Saw).flyingDir = -1;
            saw2.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw2.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw2);

            let pos3 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 1, 0));
            let saw3 = instantiate(this.sawFab);
            saw3.setPosition(pos3);
            saw3.getComponent(Saw).flyingDir = 1;
            saw3.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw3.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw3);
            let pos4 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 1, 0));
            let saw4 = instantiate(this.sawFab);
            saw4.setPosition(pos4);
            saw4.getComponent(Saw).flyingDir = -1;
            this.node.addChild(saw4);
        } else if (level == 5) {
            let pos = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 1, 0));
            let saw = instantiate(this.sawFab);
            saw.setPosition(pos);
            saw.getComponent(Saw).flyingDir = 1;
            saw.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw);
            let pos2 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 1, 0));
            let saw2 = instantiate(this.sawFab);
            saw2.setPosition(pos2);
            saw2.getComponent(Saw).flyingDir = -1;
            saw2.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw2.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw2);

            let pos3 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 1, 0));
            let saw3 = instantiate(this.sawFab);
            saw3.setPosition(pos3);
            saw3.getComponent(Saw).flyingDir = 1;
            saw3.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw3.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw3);
            let pos4 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 1, 0));
            let saw4 = instantiate(this.sawFab);
            saw4.setPosition(pos4);
            saw4.getComponent(Saw).flyingDir = -1;
            saw4.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw4.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw4);

            let pos5 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 2, 0));
            let saw5 = instantiate(this.sawFab);
            saw5.setPosition(pos5);
            saw5.getComponent(Saw).flyingDir = 1;
            saw5.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw5.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw5);
            let pos6 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 2, 0));
            let saw6 = instantiate(this.sawFab);
            saw6.setPosition(pos6);
            saw6.getComponent(Saw).flyingDir = -1;
            saw6.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw6.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw6);

            let pos7 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 2, 0));
            let saw7 = instantiate(this.sawFab);
            saw7.setPosition(pos7);
            saw7.getComponent(Saw).flyingDir = 1;
            saw7.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw7.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw7);
            let pos8 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 2, 0));
            let saw8 = instantiate(this.sawFab);
            saw8.setPosition(pos8);
            saw8.getComponent(Saw).flyingDir = -1;
            saw8.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw8.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw8);
        }
    }

    public spawnRoundBladeAtPos(pos: Vec3, dur: number) {
        let pos2 = MapUtils.getGIDByLocation(pos);
        pos2 = MapUtils.getLocationByGID(pos2);
        let blade = instantiate(this.roundBladeFab);
        blade.setPosition(pos2);
        blade.getComponent(RoundBlade).dur = dur;
        blade.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        blade.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        this.node.addChild(blade);
    }

    public spawnTwinBladeAtPos(pos: Vec3, damage: number, dur: number) {
        let pos2 = MapUtils.getGIDByLocation(pos);
        pos2 = MapUtils.getLocationByGID(pos2);
        let blade = instantiate(this.twinBladeFab);
        blade.setPosition(pos2);
        blade.getComponent(TwinBlade).damage = damage;
        blade.getComponent(TwinBlade).dur = dur;
        blade.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        blade.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        this.node.addChild(blade);
    }

    public spawnFeetSmokeAtGID(gid: Vec3) {
        let s = instantiate(this.smokeFab);
        s.getComponent(Smoke).kind = 'walk';
        let pos = MapUtils.getLocationByGID(gid);
        pos.y -= 30;
        pos.x += math.randomRange(-2, 2);
        s.setPosition(pos);
        this.node.addChild(s);
    }

    // 物体移动结束后判断是否接触地面，接触的话产生烟雾
    public checkAndSpawnLandedSmoke(pos: Vec3) {
        let gid = MapUtils.getGIDByLocation(pos);
        if (!this.gidInPlayerRange(gid)) return;

        let [tt, downGID] = this.getNearTileType(pos, 'down', 'pos');
        if (tt != GameConfig.tileKinds.Empty) {
            this.spawnSmokeAtGID(downGID);
        }
    }

    // 关卡底部生成无法破坏的石头
    spawnUnbreakableRockAtGID(gid: Vec3): Node {
        let pos = MapUtils.getLocationByGID(gid);
        let trap = instantiate(this.trap01Fab); // 也使用矿石的逻辑
        let ms = trap.getComponent(Mine) as Mine;
        ms.kind = GameConfig.blockKinds.TRAP_ROCK;
        ms.canBeBroken = false;
        ms.initialHP = 9999;
        trap.getComponent(UITransform).priority = GameConfig.UIPriority.player;
        this.scheduleOnce(() => {
            trap.setSiblingIndex(GameConfig.UIPriority.player);
        }, 0);
        trap.setPosition(pos);
        this.node.addChild(trap);
        this.blocks.push(trap);
        this.trapRocks.push(trap);

        return trap;
    }

    // 石头落下，下方的道具和矿石会被压坏
    destroyBlocksAt(pos: Vec3) {
        let a = MapUtils.getGIDByLocation(pos);
        for (let i = 0; i < this.blocks.length; i++) {
            let bs = this.blocks[i].getComponent(Mine);
            if (
                bs.kind != GameConfig.blockKinds.TRAP_ROCK &&
                bs.state == GameConfig.mineState.EXPOSED
            ) {
                let b = MapUtils.getGIDByLocation(this.blocks[i].position);
                if (a.equals(b)) {
                    this.blocks[i].destroy();
                    this.spawnSmokeAtGID(a);
                    this.spawnSmokeAtGID(a);
                    this.spawnSmokeAtGID(a);
                    this.blocks.splice(i, 1);
                    i++;
                }
            }
        }
    }

    public convertChildPositionToUI(child: Node): Vec3 {
        let pos = child.parent.getComponent(UITransform).convertToWorldSpaceAR(child.position);
        pos = this.rootNode.getComponent(UITransform).convertToNodeSpaceAR(pos);
        return pos.subtract(this.mainCamera.position);
    }

    public convertUINodePositionToMap(child: Node): Vec3 {
        let pos = child.parent.getComponent(UITransform).convertToWorldSpaceAR(child.position);
        pos = this.rootNode.getComponent(UITransform).convertToNodeSpaceAR(pos);
        return pos.subtract(this.uiCamera.position);
    }

    public startShakeCamera() {
        if (this.mainCameraState != GameConfig.cameraStates.FOLLOWING) {
            return;
        }
        this.mainCameraState = GameConfig.cameraStates.START_SHAKING;
    }

    doShakeCamera(shakeTimes: number) {
        tween(this.mainCamera)
            .by(
                0.01,
                {
                    position: new Vec3(math.randomRange(-8, 5), math.randomRange(-8, 5), 0),
                },
                {
                    onComplete: () => {
                        if (shakeTimes <= 0) {
                            this.mainCameraState = GameConfig.cameraStates.FOLLOWING;
                            return;
                        }
                        shakeTimes--;
                        this.doShakeCamera(shakeTimes);
                    },
                }
            )
            .start();
    }

    /**
     * 创建玩家出现在主场景的动画
     */
    private createAppearInMainSceneAnimation() {
        console.log(`[MAP] 🎭 开始创建主场景出现动画`);

        // 实例化 hiddenRoomMask prefab
        const maskNode = instantiate(this.hiddenRoomMaskFab);

        // 添加到Canvas
        const canvas = director.getScene().getChildByPath('Canvas');
        canvas.addChild(maskNode);

        console.log(`[MAP] 📦 HiddenRoomMask prefab实例化完成`);

        // 获取HiddenRoomMask组件
        const hiddenRoomMask = maskNode.getComponent(HiddenRoomMask);
        if (!hiddenRoomMask) {
            console.error(`[MAP] ❌ 无法获取HiddenRoomMask组件`);
            maskNode.destroy();
            return;
        }

        console.log(`[MAP] ✅ HiddenRoomMask组件获取成功`);

        // 计算玩家位置在UI坐标系中的位置
        const playerWorldPos = this.convertChildPositionToUI(this.player);

        console.log(`[MAP] 📍 玩家UI位置: (${playerWorldPos.x}, ${playerWorldPos.y})`);

        // 开始主场景出现动画
        hiddenRoomMask.startAppearAnimation(playerWorldPos, () => {
            console.log(`[MAP] ✅ 主场景出现动画完成`);
        });
    }

    update(deltaTime: number) {
        this.timePassed += deltaTime;
        if (!this.player) return;
        let ps = this.player?.getComponent(Player) as Player;
        let playerGID = MapUtils.getGIDByLocation(this.player?.position);

        // 是否发现新矿石，并且距离很近，弹出提示框
        // this.checkNewMinePopup();

        // camera
        if (this.mainCameraState == GameConfig.cameraStates.FOLLOWING) {
            let targetPos = this.player?.getComponent(Player).worldPos();
            targetPos.x = 0;
            if (this.timePassed < 1) {
                // 解决背景刚开始抖动的问题
                this.mainCamera.setPosition(targetPos);
            } else {
                if (
                    !this.cameraTargetPos ||
                    Math.abs(targetPos.y - this.mainCamera.position.y) > this.tileSize.height
                ) {
                    this.cameraTargetPos = targetPos;
                }
                this.mainCamera.setPosition(
                    this.mainCamera.position.lerp(this.cameraTargetPos, 3 * deltaTime)
                );
            }
            this.bgScript.node.setPosition(this.mainCamera.position); // 背景跟随摄像机移动
        } else if (this.mainCameraState == GameConfig.cameraStates.START_SHAKING) {
            this.mainCameraState = GameConfig.cameraStates.SHAKING;
            this.doShakeCamera(math.randomRangeInt(4, 6));
        }

        if (ps.cannotControl()) return;

        //********************
        // 矿石自动往下落
        for (let i = 0; i < this.blocks.length; i++) {
            let b = this.blocks[i] as Node;
            let bs = b.getComponent(Mine) as Mine;
            // 检查 Mine 组件是否存在
            if (!bs) {
                continue;
            }
            let mGID = MapUtils.getGIDByLocation(b.position);
            // 不能破坏的石头用于固定平台，不会下落
            if (bs.kind == GameConfig.blockKinds.TRAP_ROCK && !bs.canBeBroken) {
                continue;
            }
            // 超过这个区间的不参与计算，节省性能
            if (!this.gidInPlayerRange(mGID)) {
                continue;
            }

            // 玩家碰撞矿石，道具，收集
            if (
                b.name == GameConfig.blockKindNames.MINE &&
                bs.state == GameConfig.mineState.EXPOSED
            ) {
                if (playerGID.equals(mGID)) {
                    // 如果是隐藏房间入口，完全跳过拾取处理
                    if (
                        bs.kind == GameConfig.blockKinds.ITEM &&
                        bs.subKind == GameConfig.blockKinds.ITEM_HIDDEN_ROOM_ENTRANCE
                    ) {
                        // 隐藏房间入口不进行任何拾取处理，保持在原位
                        // 不调用pickupItem，不播放动画，只是跳过
                        continue;
                    } else {
                        this.blocks.splice(i, 1);
                        i++;
                        if (bs.kind == GameConfig.blockKinds.ITEM) {
                            // 道具
                            this.player?.getComponent(Item).pickupItem(b, bs);
                            if (bs.subKind == GameConfig.blockKinds.ITEM_HIDDEN_MONEY) {
                                b.destroy();
                            } else if (bs.subKind == GameConfig.blockKinds.ITEM_KEY) {
                                b.destroy();
                            } else if (bs.subKind == GameConfig.blockKinds.ITEM_DIAMOND) {
                                b.destroy();
                            } else if (bs.subKind == GameConfig.blockKinds.ITEM_HEART) {
                                b.destroy();
                            } else if (bs.subKind == GameConfig.blockKinds.ITEM_LAMP) {
                                b.destroy();
                            } else {
                                tween(b)
                                    .by(
                                        0.3,
                                        {
                                            position: new Vec3(0, this.tileSize.height, 0),
                                        },
                                        {
                                            onComplete: () => {
                                                b.destroy();
                                            },
                                        }
                                    )
                                    .start();
                            }
                        } else {
                            // 矿石
                            this.player?.getComponent(Item).pickupMine(b, bs);
                        }
                    }
                }
                continue;
            }
            // 陷阱伤害
            if (
                b.name == GameConfig.blockKindNames.TRAP &&
                bs.state == GameConfig.mineState.EXPOSED
            ) {
                if (playerGID.equals(mGID)) {
                    let [chapConfig, _] = this.getChapConfigByGID(mGID);
                    ps.beDamaged(
                        chapConfig.damages[bs.kind],
                        false,
                        chapConfig.traps[bs.kind] == GameConfig.blockKinds.TRAP_ROCK
                            ? 'rock'
                            : 'spike'
                    );
                }
            }
        }
    }

    /**
     * 初始化玩家，在地图生成完成后调用
     */

}
