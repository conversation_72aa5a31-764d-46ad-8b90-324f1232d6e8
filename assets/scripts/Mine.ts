import {
    _decorator,
    Component,
    director,
    Graphics,
    instantiate,
    Node,
    Prefab,
    Sprite,
    Vec3,
} from 'cc';
import { GameConfig } from './GameConfig';
import { GameData } from './GameData';
import { Item } from './Item';
import { Map } from './Map';
import { HiddenMap } from './HiddenRoom/HiddenMap';
import { Player } from './Player';
import { CommonAssetsCtl } from './v1/Common/CommonAssetsCtl';
import { UITransform } from 'cc';
import { RigidBody2D } from 'cc';
import { MapUtils } from './Utils/MapUtils';
import { HiddenRoomStateManager } from './Utils/HiddenRoomStateManager';
const { ccclass, property } = _decorator;

@ccclass('Mine')
export class Mine extends Component {
    private commonAssets; // 图片资源

    @property(Prefab)
    hpBarFab: Prefab;

    public canBeBroken = true; // 是否可以被打破，关底的石头不能被打破
    public isTreasure = false; // 是否是宝藏, 特殊类型的石头，用作关卡判定是否挖掘到宝箱

    private map: Map | HiddenMap;

    public initialHP = 2;
    public hp;
    public kind: number;
    public subKind: number; // 用于道具
    public money: number;
    public state = GameConfig.mineState.IN_DIRT;

    private g: Graphics;
    public hasDroppped = false; // 之前在空中掉落过

    private flyToPlayerSpeed = 9;
    private tmpPos: Vec3 = new Vec3();

    protected onLoad(): void {
        this.commonAssets = director
            .getScene()
            .getChildByPath('Canvas/CommonAssets')
            .getComponent(CommonAssetsCtl);

        this.hp = this.initialHP;

        this.map = director.getScene().getChildByPath('Canvas/TiledMap').getComponent(Map)
            ? director.getScene().getChildByPath('Canvas/TiledMap').getComponent(Map)
            : director.getScene().getChildByPath('Canvas/TiledMap').getComponent(HiddenMap);


        if (this.state == GameConfig.mineState.IN_DIRT) {
            this.node.getComponent(RigidBody2D) ? this.node.getComponent(RigidBody2D).enabled = false : null;
        }

        // 特殊矿石，经验值，每关都有，逻辑写死了，不能配置
        if (this.kind == GameConfig.blockKinds.MINE_A19) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m19ExpFrame;
            this.node.setScale(new Vec3(0.7, 0.7, 0.7));
            // 矿石
        } else if (this.kind == GameConfig.blockKinds.MINE_A20) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m20DesertBasicFrame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A26) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m26Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A31) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m31Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A30) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m30Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A25) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m25Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A42) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m42Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A45) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m45Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A36) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m36IceBasicFrame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A28) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m28Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A34) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m34Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A48) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m48Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A50) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m50Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A46) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m46Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A44) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m44Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A24) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m24VolcanoBasicFrame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A27) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m27Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A35) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m35Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A37) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m37Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A38) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m38Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A39) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m39Frame;
        } else if (this.kind == GameConfig.blockKinds.MINE_A41) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.m41Frame;

            // 陷阱
        } else if (this.kind == GameConfig.blockKinds.TRAP_ROCK) {
            this.state = GameConfig.mineState.EXPOSED;
            // this.map.removeTileAt(MapUtils.getGIDByLocation(this.node.position), true);
            this.node.getComponent(Sprite).spriteFrame =
                this.commonAssets[GameData.currentLevelConfig.trapRockFrame];
        } else if (this.kind == GameConfig.blockKinds.TRAP_SPIKE) {
            this.state = GameConfig.mineState.EXPOSED;
            this.map.removeTileAt(MapUtils.getGIDByLocation(this.node.position));
            this.node.getComponent(Sprite).spriteFrame =
                this.commonAssets[GameData.currentLevelConfig.trapSpikeFrame];

            // 未知道具
        } else if (this.kind == GameConfig.blockKinds.ITEM) {
            // 隐藏房间入口在土里时显示特殊图标
            if (this.subKind == GameConfig.blockKinds.ITEM_HIDDEN_ROOM_ENTRANCE) {
                this.node.getComponent(Sprite).spriteFrame =
                    this.commonAssets[GameData.currentLevelConfig.tileFrame];
            } else {
                this.node.getComponent(Sprite).spriteFrame =
                    this.commonAssets[GameData.currentLevelConfig.itemFrame];
            }
        }
    }

    setItemFrame() {
        if (this.subKind == GameConfig.blockKinds.ITEM_HEART) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.itemHeartFrame;
            this.node.getComponent(UITransform).setContentSize(30, 30);
        } else if (this.subKind == GameConfig.blockKinds.ITEM_LAMP) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.itemLampFrame;
            this.node.getComponent(UITransform).setContentSize(50, 50);
        } else if (this.subKind == GameConfig.blockKinds.ITEM_HIDDEN_MONEY) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.itemCoinFrame;
            this.node.getComponent(UITransform).setContentSize(40, 40);
        } else if (this.subKind == GameConfig.blockKinds.ITEM_KEY) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.itemKeyFrame;
            this.node.getComponent(UITransform).setContentSize(44, 44);
        } else if (this.subKind == GameConfig.blockKinds.ITEM_DIAMOND) {
            this.node.getComponent(Sprite).spriteFrame = this.commonAssets.itemDiamondFrame;
            this.node.getComponent(UITransform).setContentSize(44, 44);
        } else if (this.subKind == GameConfig.blockKinds.ITEM_HIDDEN_ROOM_ENTRANCE) {
            // 隐藏房间入口被挖出来 - 生成火山入口并保存挖掘状态
            console.log(`[MINE_DEBUG] 隐藏房间入口道具被挖出，位置: (${this.node.position.x}, ${this.node.position.y})`);

            const house = this.map.player.getComponent(Item).plantHiddenRoom();
            house.setPosition(this.node.position.clone().add(new Vec3(0, 5, 0)));

            // 保存隐藏房间入口被挖掘的状态
            const entranceGID = MapUtils.getGIDByLocation(this.node.position);
            const entranceGIDObj = { x: entranceGID.x, y: entranceGID.y };
            HiddenRoomStateManager.saveEntranceDug(entranceGIDObj);

            // 将隐藏房间入口下方的土块变成不可破坏的砖
            const belowGID = new Vec3(entranceGID.x, entranceGID.y + 1, 0);
            if (this.map instanceof Map && this.map.isBetweenWall(belowGID)) {
                console.log(`[MINE_DEBUG] 将隐藏房间入口下方土块变成不可破坏砖: (${belowGID.x}, ${belowGID.y})`);
                this.map.spawnUnbreakableRockAtGID(belowGID);
            }

            console.log(`[MINE_DEBUG] 已保存隐藏房间入口挖掘状态: GID(${entranceGID.x}, ${entranceGID.y})`);
        } else if (
            this.subKind == GameConfig.blockKinds.ITEM_BOMB_ZONE ||
            this.subKind == GameConfig.blockKinds.ITEM_BOMB_FLYING ||
            this.subKind == GameConfig.blockKinds.ITEM_BOMB_GRENADE
        ) {
            // 放置炸弹
            let bomb: Node;
            if (this.subKind === GameConfig.blockKinds.ITEM_BOMB_ZONE) {
                bomb = this.map.player
                    .getComponent(Item)
                    .plantBomb(GameConfig.blockKinds.BOMB_ZONE);
                bomb.setPosition(this.node.position.clone().add(new Vec3(0, 5, 0)));
            } else if (this.subKind === GameConfig.blockKinds.ITEM_BOMB_FLYING) {
                bomb = this.map.player
                    .getComponent(Item)
                    .plantBomb(GameConfig.blockKinds.BOMB_FLYING);
                bomb.setPosition(this.node.position.clone().add(new Vec3(0, 10, 0)));
            } else if (this.subKind === GameConfig.blockKinds.ITEM_BOMB_GRENADE) {
                bomb = this.map.player
                    .getComponent(Item)
                    .plantBomb(GameConfig.blockKinds.BOMB_GRENADE);
                bomb.setPosition(this.node.position.clone().add(new Vec3(0, 24, 0)));
            }
            // 删除自己
            for (let i = 0; i < this.map.blocks.length; i++) {
                let b = this.map.blocks[i] as Node;
                if (b.uuid == this.node.uuid) {
                    this.map.blocks.splice(i, 1);
                    break;
                }
            }
            this.node.destroy();
        }
    }

    // 返回值表示是否死亡
    public beDamaged(damage: number): boolean {
        if (!this.canBeBroken) return false;
        if (this.hp <= 0) return true;
        this.hp -= damage;
        if (this.hp <= 0) {
            this.hp = 0;
            this.state = GameConfig.mineState.EXPOSED;
            this.node.getComponent(RigidBody2D).enabled = true; // 往下掉
            this.g?.node?.destroy();

            if (this.kind == GameConfig.blockKinds.ITEM) {
                this.setItemFrame();
            } else if (this.kind == GameConfig.blockKinds.TRAP_ROCK) {
                // 陷阱类型是石头的话
                for (let i = 0; i < this.map.blocks.length; i++) {
                    if (this.map.blocks[i].uuid == this.node.uuid) {
                        this.map.blocks.splice(i, 1);
                        break;
                    }
                }
                for (let i = 0; i < this.map.trapRocks.length; i++) {
                    if (this.map.trapRocks[i].uuid == this.node.uuid) {
                        // this.map.trapRocks[i].destroy();
                        this.map.trapRocks.splice(i, 1);
                        break;
                    }
                }
                this.node.destroy();
            }

            this.map.removeTileAt(MapUtils.getGIDByLocation(this.node.position));
            return true;
        }

        // 并不是所有的东西都有血条
        if (!this.g && this.hpBarFab) {
            let h = instantiate(this.hpBarFab);
            h.setPosition(new Vec3(0, -10, 0));
            this.node.addChild(h);

            this.g = h.getComponent(Graphics);
        }
        if (this.g) {
            let width = 56;
            let height = 16;
            let round = 6;
            let border = 3;
            this.g.fillColor.fromHEX('#222222');
            this.g.roundRect(-width / 2, -height / 2, width, height, round);
            this.g.fill();

            let hpWidth = (this.hp / this.initialHP) * (width - 2 * border);
            this.g.fillColor.fromHEX('#ffffff');
            this.g.roundRect(
                -(width / 2 - border),
                -(height / 2 - border),
                hpWidth,
                height - 2 * border,
                round
            );
            this.g.fill();
        }
        return false;
    }

    public itemScale = 0.6;
    // public flyToPlayerSpeed = 0.5;
    update(deltaTime: number) {
        // 矿石和道具会自动飞到玩家身上
        if (
            this.node.name == GameConfig.blockKindNames.MINE &&
            this.state == GameConfig.mineState.EXPOSED
        ) {
            // 钻石和钥匙飞到玩家身上逐渐变大
            if (
                this.subKind == GameConfig.blockKinds.ITEM_KEY ||
                this.subKind == GameConfig.blockKinds.ITEM_DIAMOND
            ) {
                this.node.setScale(this.itemScale, this.itemScale);
                this.itemScale += deltaTime * 12;
                if (this.itemScale >= 1) {
                    this.itemScale = 1;
                }
            }

            let radius =
                this.map.player.getComponent(Player).magnetRadius +
                this.map.player.getComponent(Player).magnetRadiusDelta;
            if (Vec3.distance(this.node.position, this.map.player.position) <= radius) {
                this.tmpPos = this.node.position.lerp(
                    this.map.player.position,
                    this.flyToPlayerSpeed * deltaTime
                );
                this.node.setPosition(this.tmpPos);
            }
        }
    }
}
